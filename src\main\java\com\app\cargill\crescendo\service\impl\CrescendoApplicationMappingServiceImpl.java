/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import com.app.cargill.crescendo.model.ApplicationMapping;
import com.app.cargill.crescendo.service.ICrescendoApplicationMappingService;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
@SuppressWarnings(
    "java:S1172") // Added for unused variables, which will be incorporated during translations
// integration
public class CrescendoApplicationMappingServiceImpl implements ICrescendoApplicationMappingService {

  private final CrescendoApiServiceImpl crescendoApiService;

  @Override
  public String createApplicationMapping(
      SiteMappingDocument siteMapping,
      String accountExternalId,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {
    AccessTokenAndApiPathDto token = crescendoApiService.getTokenAndApiPath();
    return createApplicationMapping(
        token.getApiPath(),
        token.getAuthToken(),
        siteMapping,
        accountExternalId,
        bundleMessageSource,
        locale);
  }

  private String createApplicationMapping(
      String apiPath,
      AuthToken authToken,
      SiteMappingDocument siteMapping,
      String accountExternalId,
      ResourceBundleMessageSource bundleMessageSource,
      Locale locale)
      throws CustomDEExceptions {
    try {
      String applicationMappingUrl = String.format("%s/sobjects/Application_Mapping__c", apiPath);
      ApplicationMapping applicationMapping = documentToCrescendo(siteMapping, accountExternalId);
      log.info("CREATED CRESCENDO APPLICATION MAPPING STARTED {}", siteMapping);
      CreateRecordResponse recordResponse =
          crescendoApiService.createRecord(
              authToken,
              applicationMapping,
              new ParameterizedTypeReference<>() {},
              applicationMappingUrl);
      log.info("CRESCENDO_APPLICATION_MAPPING_CREATED {}", recordResponse.getId());
      return recordResponse.getId();
    } catch (Exception ex) {
      log.error(
          "CRESCENDO_APPLICATION_MAPPING_CREATE_ERROR {} , {}",
          siteMapping.getLabyrinthSiteId(),
          ex.getMessage());
      throw new CustomDEExceptions(ex.getLocalizedMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }

  private ApplicationMapping documentToCrescendo(
      SiteMappingDocument siteMapping, String accountExternalId) {
    ApplicationMapping applicationMapping = new ApplicationMapping();

    applicationMapping.setExternalSystem("LM_SITE");
    applicationMapping.setExternalRecordNum(siteMapping.getLabyrinthSiteId().toString());
    applicationMapping.setAccountId(
        accountExternalId); // pass from function in site mapping creation

    return applicationMapping;
  }
}
