/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.constants.SubTypeId;
import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoAccountService;
import com.app.cargill.crescendo.service.ICrescendoAccountTeamMemberService;
import com.app.cargill.crescendo.service.ICrescendoContactService;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.Contact;
import com.app.cargill.document.UserRole;
import com.app.cargill.dto.AccountDto;
import com.app.cargill.dto.ContactDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.IS3Service;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.constants.LiftEntityName;
import com.app.cargill.sf.cc.model.ProfileRecord;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.UserLicenseRecord;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftContactService;
import com.app.cargill.sf.cc.service.LiftUserAccessService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.app.cargill.sf.cc.utils.LiftUtils;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class AccountServiceImplTest {

  @Mock private AccountsRepository accountsRepository;

  @Mock private IS3Service s3ServiceImpl;

  @Mock private UserRepository userRepository;
  @Mock private SitesRepository sitesRepository;
  @Mock private LiftUtils liftUtils;

  @Mock private LiftConfig liftConfig;

  @Mock private LiftApiService apiService;

  @Mock private UserServiceImpl userServiceImpl;
  @Mock private LiftUserAccessService accessService;

  @Mock private LiftAccountService liftAccountService;
  @Mock private LiftContactService liftContactService;

  @Mock private LiftUserService liftUserService;

  @Mock private ICrescendoAccountService crescendoAccountService;
  @Mock private ICrescendoContactService crescendoContactService;
  @Mock private ICrescendoAccountTeamMemberService accountTeamMemberService;
  @Mock private ICrescendoUserService crescendoUserService;

  @InjectMocks private AccountServiceImpl accountService;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @Test
  void whenAccountTypeExistsCorrectItemsAreReturned() {
    when(accountsRepository.findAllBySubtypeAccountTypeSyncTimeAndAccountStatus(
            any(), any(), any(), any()))
        .thenReturn(List.of(loadAccount()));
    List<AccountDto> result = accountService.getAllAccounts("something", Instant.now(), "<EMAIL>");
    assertFalse(result.isEmpty());
    assertEquals(2, result.get(0).getUsers().size());
    assertTrue(result.get(0).getUsers().contains("user1"));
  }

  @Test
  void whenAccountTypeIsNotProvidedCorrectItemsAreReturned() {
    when(accountsRepository.findAllBySubTypeSyncTimeAndAccountStatus(any(), any(), any()))
        .thenReturn(List.of(loadAccount()));
    List<AccountDto> result = accountService.getAllAccounts("", Instant.now(), "<EMAIL>");
    assertFalse(result.isEmpty());
    assertEquals(2, result.get(0).getUsers().size());
    assertTrue(result.get(0).getUsers().contains("user1"));
  }

  @Test
  void whenBusinessIdIsCalculatedCorrectResultIsReturned() {
    assertEquals(Business.Russia.getBusinessId(), accountService.calculateBusinessId("Kazakhstan"));
    assertEquals(Business.US.getBusinessId(), accountService.calculateBusinessId("Mexico"));
    assertEquals(Business.US.getBusinessId(), accountService.calculateBusinessId("US"));
  }

  @Test
  void whenSearchNotBlankAndAccountTypeNotBlankCorrectResultsAreReturned() {
    Page<Accounts> pens = new PageImpl<>(List.of(loadAccount()));

    when(accountsRepository
            .findAllAccountsBySearchItemSubTypeIdAccountIdSyncTimeAndAccountStatusPaginated(
                any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(pens);

    Page<AccountDto> result =
        accountService.getPaginatedAccounts(
            "search", 1, 10, "id", "farmer", Instant.now(), "c@c", "desc");
    assertFalse(result.isEmpty());
  }

  @Test
  void whenSearchNotBlankAndAccountTypeIsBlankCorrectResultsAreReturned() {
    Page<Accounts> pens = new PageImpl<>(List.of(loadAccount()));

    when(accountsRepository.findAllAccountsBySearchItemSubTypeSyncTimeAndAccountStatusPaginated(
            any(), any(), any(), any(), any(), any()))
        .thenReturn(pens);

    Page<AccountDto> result =
        accountService.getPaginatedAccounts(
            "search", 1, 10, "id", "", Instant.now(), "c@c", "desc");
    assertFalse(result.isEmpty());
  }

  @Test
  void whenSearchIsBlankAndAccountTypeIsNotBlankCorrectResultsAreReturned() {
    Page<Accounts> pens = new PageImpl<>(List.of(loadAccount()));

    when(accountsRepository.findAllBySubTypeIdAccountTypeSyncTimeAndAccountStatusPaginated(
            any(), any(), any(), any(), any(), any()))
        .thenReturn(pens);

    Page<AccountDto> result =
        accountService.getPaginatedAccounts(
            "", 1, 10, "id", "farmer", Instant.now(), "c@c", "desc");
    assertFalse(result.isEmpty());
  }

  @Test
  void whenSearchIsBlankAndAccountTypeIsBlankCorrectResultsAreReturned() {
    Page<Accounts> pens = new PageImpl<>(List.of(loadAccount()));

    when(accountsRepository.findAllBySubTypeIdSyncTimeAndAccountStatusPaginated(
            any(), any(), any(), any(), any()))
        .thenReturn(pens);

    Page<AccountDto> result =
        accountService.getPaginatedAccounts("", 1, 10, "id", "", Instant.now(), "c@c", "desc");
    assertFalse(result.isEmpty());
  }

  @Test
  void whenNoResultsEmptyPageIsReturned() {

    when(accountsRepository.findAllBySubTypeIdSyncTimeAndAccountStatusPaginated(
            any(), any(), any(), any(), any()))
        .thenReturn(null);

    Page<AccountDto> result =
        accountService.getPaginatedAccounts("", 1, 10, "id", "", Instant.now(), "c@c", "desc");
    assertTrue(result.isEmpty());
  }

  @Test
  void whenAccountIdIsEmptySaveThrowsAnException() {
    AccountDto accountDto = AccountDto.builder().build();
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> accountService.save(accountDto, locale, resourceBundleMessageSource));
  }

  @Test
  void whenLocalIdExistsSaveThrowsAnException() {
    String localId = UUID.randomUUID().toString();
    AccountDto accountDto = AccountDto.builder().localId(localId).build();
    when(accountsRepository.existsByLocalId(localId)).thenReturn(true);
    Assertions.assertThrows(
        AlreadyExistsDEException.class,
        () -> accountService.save(accountDto, locale, resourceBundleMessageSource));
  }

  @Test
  void whenGetAllFilteredAccountIdsIsCalledCorrectResultIsReturned() {
    when(accountsRepository.findAllFilteredAccountIds(any(), any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    List<String> result = accountService.getFilteredAccountIds();

    assertNotNull(result);
  }

  @Test
  void whenGetAllFilteredAccountIdsIsCalledAndResultIsNullNoExceptionIsReturned() {
    when(accountsRepository.findAllFilteredAccountIds(any(), any(), any())).thenReturn(null);
    List<String> result = accountService.getFilteredAccountIds();

    assertNotNull(result);
  }

  @Test
  void whenAllWorksCorrectSaveResultIsReturned() throws Exception {
    String localId = UUID.randomUUID().toString();
    AccountDto accountDto =
        AccountDto.builder()
            .localId(localId)
            .contacts(
                List.of(
                    ContactDto.builder()
                        .firstName("test")
                        .lastName("test")
                        .email("<EMAIL>")
                        .phoneNumber("12322")
                        .build()))
            .type(8)
            .imageToUploadBase64(Base64.getEncoder().encodeToString("something".getBytes()))
            .accountType(0)
            .build();
    when(accountsRepository.existsByLocalId(any())).thenReturn(false);
    when(userRepository.findCountryIdByUserName(any())).thenReturn("Global");
    when(accountsRepository.saveAndFlush(any())).thenAnswer(i -> i.getArgument(0));

    User sfUser = new User();
    sfUser.setId("rendom-id");
    AccountDocument ad = AccountDocument.builder().goldenRecordId("random-ad-id").build();
    when(liftUserService.findOwner(any())).thenReturn(sfUser);
    when(apiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(liftAccountService.createAccount(any(), any(), any())).thenReturn(ad);
    AccountDto result = accountService.save(accountDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
    assertNotNull(result.getLastSyncTime());
  }

  @Test
  void whenAllWorksCorrectWithCrescendoDataSourceSaveResultIsReturned() throws Exception {
    String localId = UUID.randomUUID().toString();
    AccountDto accountDto =
        AccountDto.builder()
            .localId(localId)
            .contacts(
                List.of(
                    ContactDto.builder()
                        .firstName("test")
                        .lastName("test")
                        .email("<EMAIL>")
                        .phoneNumber("12322")
                        .build()))
            .type(8)
            .imageToUploadBase64(Base64.getEncoder().encodeToString("something".getBytes()))
            .accountType(0)
            .build();
    when(accountsRepository.existsByLocalId(any())).thenReturn(false);
    when(userRepository.findCountryIdByUserName(any())).thenReturn("India");
    when(accountsRepository.saveAndFlush(any())).thenAnswer(i -> i.getArgument(0));
    when(crescendoAccountService.createAccount(any(), any(), any()))
        .thenReturn("random-golden-record-id");
    when(crescendoContactService.createContact(any(), any(), any()))
        .thenReturn("random-contact-id");

    // Mock crescendoUserService.getByUserEmail
    Users mockUser = new Users();
    mockUser.setId("005XXXXXXXXXXXXXXX");
    mockUser.setEmail("<EMAIL>");
    mockUser.setIsActive(true);
    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(mockUser));
    when(crescendoUserService.getByUserEmail(any())).thenReturn(userResponse);

    User sfUser = new User();
    sfUser.setId("rendom-id");
    AccountDocument ad = AccountDocument.builder().goldenRecordId("random-ad-id").build();

    AccountDto result = accountService.save(accountDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
    assertNotNull(result.getLastSyncTime());
  }

  @Test
  void whenAllWorksCorrectAndUserIsNotOnSalesForceSaveResultIsReturned() throws Exception {
    String localId = UUID.randomUUID().toString();
    AccountDto accountDto =
        AccountDto.builder()
            .localId(localId)
            .type(8)
            .imageToUploadBase64(Base64.getEncoder().encodeToString("something".getBytes()))
            .accountType(0)
            .build();
    when(accountsRepository.existsByLocalId(any())).thenReturn(false);
    when(userRepository.findCountryIdByUserName(any())).thenReturn("Global");

    Assertions.assertThrows(
        CustomDEExceptions.class,
        () -> accountService.save(accountDto, locale, resourceBundleMessageSource));
  }

  @Test
  void whenAccountIdIsEmptyUpdateThrowsAnException() {
    AccountDto accountDto = AccountDto.builder().build();
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> accountService.update(accountDto, locale, resourceBundleMessageSource));
  }

  @Test
  void whenSaveToLiftIsCalledCorrectResultIsSaved() throws Exception {

    User user = new User();
    user.setEmail("test");
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftUserService.findUserByEmail(any())).thenReturn(user);
    when(liftAccountService.createAccount(any(), any(), any()))
        .thenReturn(loadAccount().getAccountDocument());
    when(apiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(liftContactService.saveToLift(any(), any(), any()))
        .thenReturn(loadAccount().getAccountDocument().getContacts());
    accountService.saveToLift(loadAccount(), "test", locale, resourceBundleMessageSource);

    assertNotNull(loadAccount());
  }

  @Test
  void whenSaveToLiftIsCalledForStandardLicenseUsersNoUserAccessIsCreatedCorrectResultIsSaved()
      throws Exception {

    User user = new User();
    user.setEmail("test");
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Standard");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftUserService.findUserByEmail(any())).thenReturn(user);
    when(liftAccountService.createAccount(any(), any(), any()))
        .thenReturn(loadAccount().getAccountDocument());
    when(apiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(liftContactService.saveToLift(any(), any(), any()))
        .thenReturn(loadAccount().getAccountDocument().getContacts());
    accountService.saveToLift(loadAccount(), "test", locale, resourceBundleMessageSource);

    assertNotNull(loadAccount());
  }

  @Test
  void whenSaveToLiftIsCalledButNoUserAccessIsCreatedCorrectResultIsSaved() throws Exception {

    User user = new User();
    user.setEmail("test");
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Standard");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftAccountService.createAccount(any(), any(), any()))
        .thenReturn(loadAccount().getAccountDocument());
    when(apiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(liftContactService.saveToLift(any(), any(), any()))
        .thenReturn(loadAccount().getAccountDocument().getContacts());
    accountService.saveToLift(loadAccount(), "test", locale, resourceBundleMessageSource);

    assertNotNull(loadAccount());
  }

  @Test
  void whenCreateUserRolesIsCalledCorrectListIsReturned() {
    assertNotNull(accountService.createUserRoles("<EMAIL>"));
  }

  @Test
  void whenCreateUserRoleIfNotExistIsCalledAndUserRolesSavedInDbArePresent() {
    User user = new User();
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findUserByEmail(any())).thenReturn(user);
    Accounts account =
        Accounts.builder()
            .accountDocument(
                AccountDocument.builder()
                    .userRoles(
                        List.of(
                            UserRole.builder()
                                .roleType("AccountRepresentative")
                                .userName("<EMAIL>")
                                .build()))
                    .build())
            .build();
    accountService.createUserRoleIfNotExist(account);
    assertNotNull(account);
  }

  @Test
  void whenCreateUserRoleIfNotExistIsCalledAndUserRolesAreNotSavedInDbArePresent() {
    User user = new User();
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findUserByEmail(any())).thenReturn(user);
    Accounts account =
        Accounts.builder().accountDocument(AccountDocument.builder().build()).build();
    accountService.createUserRoleIfNotExist(account);
    assertNotNull(account);
  }

  @Test
  void
      whenCreateUserRoleIfNotExistIsCalledAndUserRolesSavedInDbArePresentAndUserRoleAlreadyExists() {
    User user = new User();
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    when(liftUserService.findUserByEmail(any())).thenReturn(user);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    Accounts account =
        Accounts.builder()
            .accountDocument(
                AccountDocument.builder()
                    .userRoles(
                        List.of(
                            UserRole.builder()
                                .roleType("AccountRepresentative")
                                .userName("<EMAIL>")
                                .build()))
                    .build())
            .build();
    accountService.createUserRoleIfNotExist(account);
    assertNotNull(account);
  }

  @Test
  void createUserRoleInvokedForCsnUsers() {
    User user1 = new User();
    user1.setEmail("<EMAIL>");
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setGoldenRecordId("123");
    Accounts account = new Accounts(accountDocument);

    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user1.setProfile(profileRecord);
    when(liftUserService.findUserByEmail(any())).thenReturn(user1);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");

    accountService.createUserRoleIfNotExist(account);
    assertNotNull(account);
  }

  @Test
  void whenSaveToLiftIsCalledExceptionIsThrownOnValidationsFailing() throws Exception {

    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(apiService.validate(any(), any(), any(), any()))
        .thenReturn(
            PayloadValidationDto.builder()
                .errorDetails(
                    List.of(
                        LiftResponseEntityDto.builder()
                            .message("test")
                            .status(ResponseStatus.FAILED)
                            .build()))
                .build());

    Assertions.assertThrows(
        CustomDEExceptions.class,
        () ->
            accountService.saveToLift(loadAccount(), "test", locale, resourceBundleMessageSource));
  }

  @Test
  void whenUpdateContactOnSalesforceIsCalledExceptionIsThrownOnAccountsValidationsFailing()
      throws Exception {

    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(apiService.validate(any(), any(), any(), any()))
        .thenReturn(
            PayloadValidationDto.builder()
                .errorDetails(
                    List.of(
                        LiftResponseEntityDto.builder()
                            .message("test")
                            .status(ResponseStatus.FAILED)
                            .build()))
                .build());

    Assertions.assertThrows(
        CustomDEExceptions.class,
        () ->
            accountService.updateContactOnLift(
                loadAccountWithGoldenRecordId(), locale, resourceBundleMessageSource));
  }

  @Test
  void whenUpdateContactOnSalesforceIsCalledExceptionIsThrownOnValidationsFailing()
      throws Exception {

    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(apiService.validate(any(), eq(LiftEntityName.ACCOUNT), any(), any()))
        .thenReturn(PayloadValidationDto.builder().errorDetails(new ArrayList<>()).build());
    when(apiService.validate(any(), eq(LiftEntityName.CONTACT), any(), any()))
        .thenReturn(
            PayloadValidationDto.builder()
                .errorDetails(
                    List.of(
                        LiftResponseEntityDto.builder()
                            .message("test")
                            .status(ResponseStatus.FAILED)
                            .build()))
                .build());

    Assertions.assertThrows(
        CustomDEExceptions.class,
        () ->
            accountService.updateContactOnLift(
                loadAccountWithGoldenRecordId(), locale, resourceBundleMessageSource));
  }

  @Test
  void whenSaveToLiftIsCalledExceptionIsThrownOnContactsValidationsFailing() throws Exception {

    User user = new User();
    user.setEmail("test");
    user.setUserType("CsnOnly");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(apiService.validate(any(), eq(LiftEntityName.ACCOUNT), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(apiService.validate(any(), eq(LiftEntityName.CONTACT), any(), any()))
        .thenReturn(
            PayloadValidationDto.builder()
                .errorDetails(
                    List.of(
                        LiftResponseEntityDto.builder()
                            .message("test")
                            .status(ResponseStatus.FAILED)
                            .build()))
                .build());

    Assertions.assertThrows(
        CustomDEExceptions.class,
        () ->
            accountService.saveToLift(loadAccount(), "test", locale, resourceBundleMessageSource));
  }

  @Test
  void whenAccountDoesNotExistUpdateThrowsAnException() {
    AccountDto accountDto =
        AccountDto.builder()
            .id(UUID.randomUUID())
            .imageToUploadBase64(Base64.getEncoder().encodeToString("something".getBytes()))
            .accountType(0)
            .build();
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> accountService.update(accountDto, locale, resourceBundleMessageSource));
  }

  @Test
  void whenAllWorksCorrectUpdateResultIsReturned() throws Exception {
    Accounts testAccount = loadAccount();
    AccountDto accountDto =
        AccountDto.builder()
            .id(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .imageToUploadBase64(Base64.getEncoder().encodeToString("something".getBytes()))
            .accountType(0)
            .type(8)
            .contacts(
                List.of(
                    ContactDto.builder()
                        .contactId(UUID.randomUUID())
                        .sfdcId("sf-1")
                        .firstName("test")
                        .lastName("test")
                        // .fullName("Mr John Doe")
                        .build()))
            .build();
    when(accountsRepository.findByAccountId(any())).thenReturn(testAccount);
    when(sitesRepository.countByAccountId(any())).thenReturn(0);
    when(accountsRepository.save(any())).thenReturn(testAccount);
    AccountDto result = accountService.update(accountDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
    assertNotNull(result.getLastSyncTime());
  }

  @Test
  void whenAllWorksCorrectUpdateResultIsReturnedWithNoContacts() throws Exception {
    AccountDocument accountDocument =
        AccountDocument.builder()
            .id(UUID.randomUUID())
            .users(Set.of("user1", "user2"))
            .physicalAddress(Address.builder().build())
            .build();
    Accounts testAccount =
        Accounts.builder()
            .accountDocument(accountDocument)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .build();

    AccountDto accountDto =
        AccountDto.builder()
            .id(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .imageToUploadBase64(Base64.getEncoder().encodeToString("something".getBytes()))
            .dateOfLastVisit(Instant.now())
            .accountType(0)
            .type(null)
            .contacts(
                List.of(
                    ContactDto.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("test")
                        .lastName("test")
                        // .fullName("Mr John Doe")
                        .build()))
            .build();
    when(accountsRepository.findByAccountId(any())).thenReturn(testAccount);
    when(sitesRepository.countByAccountId(any())).thenReturn(0);
    when(accountsRepository.save(any())).thenReturn(testAccount);
    AccountDto result = accountService.update(accountDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
    assertNotNull(result.getLastSyncTime());
  }

  @Test
  void whenCreateUserRolesOnCrescendoIsCalledCorrectListIsReturned() {
    List<UserRole> userRoles = accountService.createCrescendoUserRole();
    assertNotNull(userRoles);
  }

  @Test
  void whenCreateUserRolesIfNotExistsOnCrescendoIsCalledCorrectListIsReturned() {
    UserRole userRole =
        UserRole.builder().roleType("Dairy Focus Consultant").userName("<EMAIL>").build();
    List<UserRole> userRoles = new ArrayList<>();
    userRoles.add(userRole);

    AccountDocument accountDocument = AccountDocument.builder().userRoles(userRoles).build();
    accountService.createCrescendoUserRolesIfNotExist(
        Accounts.builder().accountDocument(accountDocument).build());
    assertNotNull(accountDocument.getUserRoles());
  }

  @Test
  void whenUserIsNotFoundInUpdateContactOnSalesForceExceptionIsThrown() {
    when(liftUserService.findOwner(any())).thenReturn(null);

    Assertions.assertThrows(
        CustomDEExceptions.class,
        () -> accountService.updateContactOnLift(null, locale, resourceBundleMessageSource));
  }

  private Accounts loadAccount() {
    AccountDocument accountDocument =
        AccountDocument.builder()
            .id(UUID.randomUUID())
            .users(Set.of("user1", "user2"))
            .contacts(
                List.of(
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .sFDCContactId("sf-1")
                        .firstName("John")
                        .lastName("Doe")
                        .build(),
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("Jane")
                        .lastName("Doe")
                        .build()))
            .active(true)
            .subTypeID(SubTypeId.FarmProducer)
            .physicalAddress(Address.builder().build())
            .dateOfLastVisit(Instant.now())
            .build();
    return Accounts.builder()
        .accountDocument(accountDocument)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  private Accounts loadAccountWithGoldenRecordId() {
    AccountDocument accountDocument =
        AccountDocument.builder()
            .id(UUID.randomUUID())
            .goldenRecordId(UUID.randomUUID().toString())
            .users(Set.of("user1", "user2"))
            .contacts(
                List.of(
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .sFDCContactId("sf-1")
                        .firstName("John")
                        .lastName("Doe")
                        .build(),
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("Jane")
                        .lastName("Doe")
                        .build()))
            .active(true)
            .subTypeID(SubTypeId.FarmProducer)
            .physicalAddress(Address.builder().build())
            .dateOfLastVisit(Instant.now())
            .build();
    return Accounts.builder()
        .accountDocument(accountDocument)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  @Test
  void whenSaveCorrectAndCountryIdpresent() throws Exception {
    String localId = UUID.randomUUID().toString();
    AccountDto accountDto =
        AccountDto.builder()
            .localId(localId)
            .contacts(
                List.of(
                    ContactDto.builder()
                        .firstName("test")
                        .lastName("test")
                        .email("<EMAIL>")
                        .phoneNumber("12322")
                        .build()))
            .type(8)
            .imageToUploadBase64(Base64.getEncoder().encodeToString("something".getBytes()))
            .accountType(0)
            .country("US")
            .build();
    when(accountsRepository.existsByLocalId(any())).thenReturn(false);
    when(userRepository.findCountryIdByUserName(any())).thenReturn("Global");
    when(accountsRepository.saveAndFlush(any())).thenAnswer(i -> i.getArgument(0));

    User sfUser = new User();
    sfUser.setId("rendom-id");
    AccountDocument ad = AccountDocument.builder().goldenRecordId("random-ad-id").build();
    when(liftUserService.findOwner(any())).thenReturn(sfUser);
    when(apiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(liftAccountService.createAccount(any(), any(), any())).thenReturn(ad);
    AccountDto result = accountService.save(accountDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
    assertNotNull(result.getLastSyncTime());
  }
}
