/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.LactationStage;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.crescendo.model.EventsCrescendo;
import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoActivityService;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.AnimalAnalysisDetailsToolItem;
import com.app.cargill.document.AnimalAnalysisTool;
import com.app.cargill.document.AnimalAnalysisToolItem;
import com.app.cargill.document.BodyConditionTool;
import com.app.cargill.document.CalfHeiferScorecard;
import com.app.cargill.document.Contact;
import com.app.cargill.document.CudChewingByPen;
import com.app.cargill.document.CudChewingCount;
import com.app.cargill.document.CudChewingCowCount;
import com.app.cargill.document.CudChewingTool;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.EventDocument;
import com.app.cargill.document.ForagePennStateTool;
import com.app.cargill.document.HeatStressTool;
import com.app.cargill.document.HerdAnalysisGoal;
import com.app.cargill.document.LocomotionTool;
import com.app.cargill.document.ManureScreenerTool;
import com.app.cargill.document.MetabolicIncidenceTool;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.PenTimeBudgetTool;
import com.app.cargill.document.ProfitabilityAnalysisTool;
import com.app.cargill.document.ReturnOverFeedTool;
import com.app.cargill.document.RoboticMilkEvaluationTool;
import com.app.cargill.document.RumenFillTool;
import com.app.cargill.document.RumenHealthManureScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreTool;
import com.app.cargill.document.RumenHealthTool;
import com.app.cargill.document.RumenHealthToolItem;
import com.app.cargill.document.Scorecard;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteVisit;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.AnimalAnalysisDetailsToolItemDto;
import com.app.cargill.dto.AnimalAnalysisToolDto;
import com.app.cargill.dto.AnimalAnalysisToolItemDto;
import com.app.cargill.dto.BodyConditionToolDto;
import com.app.cargill.dto.CalfHeiferScorecardDto;
import com.app.cargill.dto.ForagePennStateToolDto;
import com.app.cargill.dto.HeatStressToolDto;
import com.app.cargill.dto.ISelectDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.LocomotionToolDto;
import com.app.cargill.dto.ManureScreenerToolDto;
import com.app.cargill.dto.MetabolicIncidenceToolDto;
import com.app.cargill.dto.MilkSoldEvaluationToolDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.dto.PenTimeBudgetToolDto;
import com.app.cargill.dto.ProfitabilityAnalysisToolDto;
import com.app.cargill.dto.ReturnOverFeedToolDto;
import com.app.cargill.dto.RoboticMilkEvaluationToolDto;
import com.app.cargill.dto.RumenFillToolDto;
import com.app.cargill.dto.RumenHealthManureScoreToolDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreToolDto;
import com.app.cargill.dto.RumenHealthToolDto;
import com.app.cargill.dto.ScorecardDto;
import com.app.cargill.dto.VisitDto;
import com.app.cargill.dto.VisitPublishDto;
import com.app.cargill.dto.VisitPublishResponseDto;
import com.app.cargill.dto.VisitSearchDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.filterspecification.FilterSpecification;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Activities;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.impl.mappers.VisitMapper;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftEventService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.app.cargill.sf.cc.utils.LiftUtils;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
class VisitServiceImplTest {

  @Mock private AccountsRepository accountsRepository;

  @Mock private SitesRepository sitesRepository;

  @Mock private VisitsRepository visitsRepository;

  @Mock private LiftApiService liftApiService;

  @Mock private VisitMapper visitMapper;

  @Mock private IUserService userServiceImpl;

  @Mock private LiftEventService liftEventService;

  @Mock private ActivitiesRepository activitiesRepository;

  @Mock private LiftUtils liftUtils;

  @Mock private SiteMappingsRepository siteMappingsRepository;

  @Mock private LiftUserService liftUserService;

  @Mock private ICrescendoActivityService crescendoActivityService;

  @Mock private ICrescendoUserService crescendoUserService;

  @InjectMocks private VisitServiceImpl visitService;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @Test
  void GetAllVisitsReturnExpectedResults() {
    List<Visits> visits = new ArrayList<>();
    visits.add(loadVisit());
    when(visitsRepository.findAll()).thenReturn(visits);

    List<VisitDto> result = visitService.fetchAllVisits();
    assertEquals(1L, result.size());
    assertNotNull(result);
  }

  @Test
  void GetAllVisitsReturnEmptyList() {
    when(visitsRepository.findAll()).thenReturn(new ArrayList<>());
    Assertions.assertThrows(NotFoundDEException.class, () -> visitService.fetchAllVisits());
  }

  @Test
  void UserIdReturnsVisitsCorrectlyInOfflineMode() {
    Page<Visits> visits = new PageImpl<>(List.of(loadVisit()));
    List<String> accountIds = new ArrayList<>();
    List<String> visitIds = new ArrayList<>();
    visitIds.add(UUID.randomUUID().toString());
    accountIds.add(UUID.randomUUID().toString());
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any())).thenReturn(accountIds);
    when(sitesRepository.findVisitidsByAccountIds(any())).thenReturn(visitIds);
    when(visitsRepository.findByVisitIdsOfCurrentLoggedInUser(any(), any(), any()))
        .thenReturn(visits);
    Page<VisitDto> result =
        visitService.getAllVisitsByCurrentLoggedInUser(1, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertFalse(result.isEmpty());
  }

  @Test
  void InOfflineModewhenNoVisitssForCurrentUserAreFoundEmptyPageIsReturned() {
    List<String> accountIds = new ArrayList<>();
    List<String> visitIds = new ArrayList<>();
    visitIds.add(UUID.randomUUID().toString());
    accountIds.add(UUID.randomUUID().toString());
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any())).thenReturn(accountIds);
    when(sitesRepository.findVisitidsByAccountIds(any())).thenReturn(visitIds);
    when(visitsRepository.findByVisitIdsOfCurrentLoggedInUser(any(), any(), any()))
        .thenReturn(null);
    Page<VisitDto> result =
        visitService.getAllVisitsByCurrentLoggedInUser(1, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
  }

  @Test
  void WhenAllFieldsAreAvailableCorrectDataIsReturned() {
    Page<Visits> visits = new PageImpl<>(List.of(loadVisitWithNoTools()));
    lenient()
        .when(visitsRepository.findAll(any(FilterSpecification.class), any(Pageable.class)))
        .thenReturn(visits);
    when(sitesRepository.findBySiteIds(any())).thenReturn(List.of(loadSites()));
    when(accountsRepository.findAccountsByAccountIds(any())).thenReturn(List.of(loadAccount()));
    //  when(accountsRepository.findAccountsByUser(any())).thenReturn(List.of(loadAccount()));
    //  when(sitesRepository.findByCurrentLoggedInUser(any())).thenReturn(List.of(loadSites()));
    Page<VisitDto> result =
        visitService.getAllVisitsBySearchPaginated(
            0,
            10,
            "id",
            Instant.now(),
            "desc",
            mapSearchDto(
                Instant.parse("2022-10-10T09:50:03.028Z"),
                Instant.parse("2022-10-05T09:50:03.028Z")));
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenGetVisitIdAndNameListReturnSuccess() {
    Page<ISelectDto> visits = new PageImpl<>(List.of());
    lenient().when(visitsRepository.findByAccountIds(any(), any(), any())).thenReturn(visits);

    Page<ISelectDto> result =
        visitService.getVisitIdAndNameList(0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(0L, result.getTotalElements());
  }

  @Test
  void WhenAllFieldsAreAvailableCorrectDataIsReturnedWithNoCustomerOrSiteIds() {
    Page<Visits> visits = new PageImpl<>(List.of(loadVisitWithNoTools()));
    lenient()
        .when(visitsRepository.findAll(any(FilterSpecification.class), any(Pageable.class)))
        .thenReturn(visits);
    //    when(sitesRepository.findBySiteIds(any())).thenReturn(List.of(loadSites()));
    //
    // when(accountsRepository.findAccountsByAccountIds(any())).thenReturn(List.of(loadAccount()));
    when(accountsRepository.findAccountsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(loadAccount()));
    when(sitesRepository.findSitesByAccountIds(any())).thenReturn(List.of(loadSites()));
    Page<VisitDto> result =
        visitService.getAllVisitsBySearchPaginated(
            0, 10, "id", Instant.now(), "desc", mapSearchDtoForNull());
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  private VisitSearchDto mapSearchDto(Instant to, Instant from) {
    List<String> customerIds = new ArrayList<>();
    customerIds.add("508c3253-a00b-4d58-bfaa-3a87f68d91ae");
    List<String> siteIds = new ArrayList<>();
    siteIds.add("********-9ea9-475a-a0fc-39dc8357d44a");
    List<String> visitStatuses = new ArrayList<>();
    visitStatuses.add("InProgress");
    List<String> tools = new ArrayList<>();
    tools.add("RumenHealth");
    return new VisitSearchDto(visitStatuses, to, from, customerIds, siteIds, tools);
  }

  private VisitSearchDto mapSearchDtoForNull() {
    return new VisitSearchDto(null, null, null, null, null, null);
  }

  @Test
  void WhenNoFieldsAreAvailableEmptyListIsReturned() {
    lenient()
        .when(visitsRepository.findAll(any(FilterSpecification.class), any(Pageable.class)))
        .thenReturn(new PageImpl<>(new ArrayList<>()));
    Page<VisitDto> result =
        visitService.getAllVisitsBySearchPaginated(
            0,
            10,
            "id",
            Instant.now(),
            "desc",
            new VisitSearchDto(null, null, null, null, null, null));
    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
  }

  @Test
  void WhenVisitIsPublishedDuringCreation() throws Exception {
    VisitDto visitDto =
        VisitDto.builder()
            .localId(UUID.randomUUID().toString())
            .visitDate(Instant.now())
            .status(VisitStatus.Published)
            .id(UUID.randomUUID())
            .deleted(false)
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .build();

    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(visitsRepository.existsByLocalId(any())).thenReturn(false);
    when(visitsRepository.save(any())).thenReturn(loadVisit());
    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccount());
    when(activitiesRepository.existByVisitId(any())).thenReturn(false);
    when(activitiesRepository.save(any())).thenReturn(loadActivities());
    VisitDto result = visitService.save(visitDto, locale, resourceBundleMessageSource);
    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void WhenAccountIdNotFoundDuringActivityCreation() {
    VisitDto visitDto =
        VisitDto.builder()
            .localId(UUID.randomUUID().toString())
            .visitDate(Instant.now())
            .status(VisitStatus.Published)
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .build();
    when(visitsRepository.existsByLocalId(any())).thenReturn(false);
    when(accountsRepository.findByAccountId(any())).thenReturn(null);
    when(activitiesRepository.existByVisitId(any())).thenReturn(false);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> visitService.save(visitDto, locale, resourceBundleMessageSource));
  }

  @Test
  void WhenLocalIdExistsSaveThrowsAnException() {
    String localId = UUID.randomUUID().toString();
    VisitDto visitDto = VisitDto.builder().localId(localId).build();
    when(visitsRepository.existsByLocalId(localId)).thenReturn(true);
    Assertions.assertThrows(
        AlreadyExistsDEException.class,
        () -> visitService.save(visitDto, locale, resourceBundleMessageSource));
  }

  @Test
  void WhenVisitIdIsEmptySaveThrowsAnException() {
    VisitDto visitDto = VisitDto.builder().build();
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> visitService.save(visitDto, locale, resourceBundleMessageSource));
  }

  @Test
  void WhenSiteIdDoesNotExistUpdateThrowsAnException() {
    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .rumenHealth(visitMapper.modelToDtoForRumenHealth(RumenHealthTool.builder().build()))
            .bodyCondition(visitMapper.modelToDtoForBCS(BodyConditionTool.builder().build()))
            .deleted(false)
            .locomotionScore(visitMapper.modelToDtoForLocomotion(LocomotionTool.builder().build()))
            .metabolicIncidence(
                visitMapper.modelToDtoForMetabolicIncidence(
                    MetabolicIncidenceTool.builder().build()))
            .build();
    lenient().when(visitsRepository.existsByLocalId(any())).thenReturn(false);
    lenient().when(sitesRepository.findBySiteId(any())).thenReturn(null);
    lenient().when(activitiesRepository.existByVisitId(any())).thenReturn(false);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> visitService.save(visitDto, locale, resourceBundleMessageSource));
  }

  @Test
  void WhenSiteDoesNotExistUpdateThrowsAnException() {
    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .rumenHealth(RumenHealthToolDto.builder().build())
            .bodyCondition(BodyConditionToolDto.builder().build())
            .locomotionScore(LocomotionToolDto.builder().build())
            .metabolicIncidence(MetabolicIncidenceToolDto.builder().build())
            .roboticMilkEvaluation(RoboticMilkEvaluationToolDto.builder().build())
            .deleted(false)
            .build();
    lenient().when(visitsRepository.existsByLocalId(any())).thenReturn(false);
    lenient().when(sitesRepository.findBySiteId(any())).thenReturn(null);
    lenient().when(activitiesRepository.existByVisitId(any())).thenReturn(false);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> visitService.save(visitDto, locale, resourceBundleMessageSource));
  }

  @Test
  void WhenVisitDoesNotExistUpdateThrowsAnException() {
    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            .rumenHealth(visitMapper.modelToDtoForRumenHealth(RumenHealthTool.builder().build()))
            .bodyCondition(BodyConditionToolDto.builder().build())
            .locomotionScore(LocomotionToolDto.builder().build())
            .metabolicIncidence(MetabolicIncidenceToolDto.builder().build())
            .animalAnalysis(
                AnimalAnalysisToolDto.builder()
                    .visitId(UUID.randomUUID())
                    .animals(Arrays.asList(AnimalAnalysisToolItemDto.builder().build()))
                    .build())
            .build();
    when(visitsRepository.findByVisitId(any())).thenReturn(null);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> visitService.update(visitDto, locale, resourceBundleMessageSource));
  }

  @Test
  void
      WhenModelVisitStatusIsInProgressAndDtoVisitStatusIsPublishedAndNoActivityIsFoundResultReturnedCorrectly()
          throws Exception {
    VisitDto visitDto =
        VisitDto.builder()
            .visitDate(Instant.now())
            .formattedCreationDate("test")
            .visitPublishedDateTimeUtc(Instant.now())
            .visitName("Test Update")
            .isVisitAutoPublished(true)
            .firstName("Test")
            .lastName("test")
            .selected(true)
            .selectedCurrency(Currencies.ARS)
            .status(VisitStatus.Published)
            .id(UUID.randomUUID())
            .status(VisitStatus.Published)
            //  .cudChewing(CudChewingTool.builder().build())
            .rumenHealth(RumenHealthToolDto.builder().id(UUID.randomUUID()).build())
            .metabolicIncidence(MetabolicIncidenceToolDto.builder().id(UUID.randomUUID()).build())
            .bodyCondition(BodyConditionToolDto.builder().id(UUID.randomUUID()).build())
            .rumenFillManureScore(RumenFillToolDto.builder().id(UUID.randomUUID()).build())
            .milkSoldEvaluation(MilkSoldEvaluationToolDto.builder().build())
            .locomotionScore(LocomotionToolDto.builder().id(UUID.randomUUID()).build())
            .animalAnalysis(AnimalAnalysisToolDto.builder().id(UUID.randomUUID()).build())
            .tmrParticleScore(
                RumenHealthTMRParticleScoreToolDto.builder().id(UUID.randomUUID()).build())
            .foragePennState(ForagePennStateToolDto.builder().id(UUID.randomUUID()).build())
            .roboticMilkEvaluation(
                RoboticMilkEvaluationToolDto.builder().id(UUID.randomUUID()).build())
            .rumenHealthManureScore(
                RumenHealthManureScoreToolDto.builder().id(UUID.randomUUID()).build())
            .manureScreenerTool(ManureScreenerToolDto.builder().id(UUID.randomUUID()).build())
            .heatStress(HeatStressToolDto.builder().id(UUID.randomUUID()).build())
            .penTimeBudgetTool(PenTimeBudgetToolDto.builder().id(UUID.randomUUID()).build())
            .forageAuditScorecard(ScorecardDto.builder().id(UUID.randomUUID()).build())
            .profitabilityAnalysisTool(
                ProfitabilityAnalysisToolDto.builder().id(UUID.randomUUID()).build())
            .calfHeiferScorecard(CalfHeiferScorecardDto.builder().build())
            .returnOverFeedTool(ReturnOverFeedToolDto.builder().build())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .build();
    when(visitsRepository.findByVisitId(any())).thenReturn(loadVisit());
    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccount());
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(visitsRepository.save(any())).thenReturn(loadVisit());
    when(visitMapper.dtoToModelForLocomotion(any()))
        .thenReturn(loadVisit().getVisitDocument().getLocomotionScore());
    when(visitMapper.dtoToModelForRumenHealth(any()))
        .thenReturn(loadVisit().getVisitDocument().getRumenHealth());
    when(visitMapper.dtoToModelForBCS(any()))
        .thenReturn(loadVisit().getVisitDocument().getBodyCondition());
    when(visitMapper.dtoToModelForMetabolicIncidence(any()))
        .thenReturn(loadVisit().getVisitDocument().getMetabolicIncidence());
    when(visitMapper.dtoToModelForAnimalAnalysis(any()))
        .thenReturn(loadVisit().getVisitDocument().getAnimalAnalysis());
    when(visitMapper.dtoToModelForRoboticMilkEvaluation(any()))
        .thenReturn(loadVisit().getVisitDocument().getRoboticMilkEvaluation());
    when(visitMapper.dtoToModelForRumenHealthManureScore(any()))
        .thenReturn(loadVisit().getVisitDocument().getRumenHealthManureScore());
    when(visitMapper.dtoToModelForRumenFillManureScore(any()))
        .thenReturn(loadVisit().getVisitDocument().getRumenFillManureScore());
    when(visitMapper.dtoToModelForTmrParticleScore(any()))
        .thenReturn(loadVisit().getVisitDocument().getTmrParticleScore());
    when(visitMapper.dtoToModelForForagePennState(any()))
        .thenReturn(loadVisit().getVisitDocument().getForagePennState());
    when(visitMapper.dtoToModelForHeatStress(any()))
        .thenReturn(loadVisit().getVisitDocument().getHeatStress());
    when(visitMapper.dtoToModelForPenTimeBudget(any()))
        .thenReturn(loadVisit().getVisitDocument().getPenTimeBudgetTool());
    when(visitMapper.dtoToModelForManureScreenerTool(any()))
        .thenReturn(loadVisit().getVisitDocument().getManureScreenerTool());
    when(visitMapper.dtoToModelForForageAuditScorecard(any()))
        .thenReturn(loadVisit().getVisitDocument().getForageAuditScorecard());
    when(visitMapper.dtoToModelForProfitabilityAnalysis(any()))
        .thenReturn(ProfitabilityAnalysisTool.builder().build());
    when(visitMapper.dtoToModelForCalfHeiferScorecard(any()))
        .thenReturn(CalfHeiferScorecard.builder().build());
    when(visitMapper.dtoToModelForReturnOverFeed(any()))
        .thenReturn(ReturnOverFeedTool.builder().build());

    when(activitiesRepository.save(any())).thenReturn(loadActivities());
    VisitDto result = visitService.update(visitDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void
      WhenModelVisitStatusIsInProgressAndDtoVisitStatusIsPublishedAndNoActivityIsFoundWithNoToolIdsResultReturnedCorrectly()
          throws Exception {
    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            //  .cudChewing(CudChewingTool.builder().build())
            .rumenHealth(RumenHealthToolDto.builder().id(null).build())
            .bodyCondition(BodyConditionToolDto.builder().id(null).build())
            .metabolicIncidence(MetabolicIncidenceToolDto.builder().id(null).build())
            .locomotionScore(LocomotionToolDto.builder().id(null).build())
            .milkSoldEvaluation(MilkSoldEvaluationToolDto.builder().id(null).build())
            .roboticMilkEvaluation(RoboticMilkEvaluationToolDto.builder().id(null).build())
            .rumenHealthManureScore(RumenHealthManureScoreToolDto.builder().id(null).build())
            .tmrParticleScore(RumenHealthTMRParticleScoreToolDto.builder().id(null).build())
            .foragePennState(ForagePennStateToolDto.builder().id(null).build())
            .penTimeBudgetTool(PenTimeBudgetToolDto.builder().id(null).build())
            .heatStress(HeatStressToolDto.builder().id(null).build())
            .manureScreenerTool(ManureScreenerToolDto.builder().id(null).build())
            .forageAuditScorecard(ScorecardDto.builder().id(null).build())
            .profitabilityAnalysisTool(ProfitabilityAnalysisToolDto.builder().id(null).build())
            .calfHeiferScorecard(CalfHeiferScorecardDto.builder().id(null).build())
            .returnOverFeedTool(ReturnOverFeedToolDto.builder().id(null).build())
            .animalAnalysis(
                AnimalAnalysisToolDto.builder()
                    .visitId(UUID.randomUUID())
                    .animals(Arrays.asList(AnimalAnalysisToolItemDto.builder().build()))
                    .build())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .build();
    when(visitsRepository.findByVisitId(any())).thenReturn(loadVisit());
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(visitsRepository.save(any())).thenReturn(loadVisit());
    VisitDto result = visitService.update(visitDto, locale, resourceBundleMessageSource);
    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void WhenSiteVisitReturnsNullNotFoundExceptionThrow() {
    VisitDto visitDto =
        VisitDto.builder()

            //  .cudChewing(CudChewingTool.builder().build())
            .rumenHealth(visitMapper.modelToDtoForRumenHealth(RumenHealthTool.builder().build()))
            .bodyCondition(visitMapper.modelToDtoForBCS(BodyConditionTool.builder().build()))
            .milkSoldEvaluation(
                visitMapper.modelToDtoForMilkSoldEvaluation(
                    MilkSoldEvaluationTool.builder().build()))
            .rumenHealthManureScore(
                visitMapper.modeltoDtoForRumenHealthManureScore(
                    RumenHealthManureScoreTool.builder().build()))
            .metabolicIncidence(
                visitMapper.modelToDtoForMetabolicIncidence(
                    MetabolicIncidenceTool.builder().build()))
            .roboticMilkEvaluation(
                visitMapper.modelToDtoForRoboticMilkEvaluation(
                    RoboticMilkEvaluationTool.builder().build()))
            .rumenFillManureScore(
                visitMapper.modelToDtoForRumenFillManureScore(RumenFillTool.builder().build()))
            .locomotionScore(visitMapper.modelToDtoForLocomotion(LocomotionTool.builder().build()))
            .animalAnalysis(
                AnimalAnalysisToolDto.builder()
                    .visitId(UUID.randomUUID())
                    .animals(
                        Arrays.asList(
                            AnimalAnalysisToolItemDto.builder()
                                .penId(UUID.randomUUID())
                                .animalDetails(
                                    Arrays.asList(
                                        AnimalAnalysisDetailsToolItemDto.builder().build()))
                                .build()))
                    .build())
            .visitDate(Instant.now())
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .selectedCurrency(Currencies.CAD)
            .build();
    when(sitesRepository.findBySiteId(any())).thenReturn(null);
    lenient().when(visitsRepository.findByVisitId(any())).thenReturn(loadVisit());
    lenient().when(activitiesRepository.existByVisitId(any())).thenReturn(true);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> visitService.update(visitDto, locale, resourceBundleMessageSource));
  }

  @Test
  void WhenAllWorksCorrectSaveResultIsReturned() throws Exception {
    String localId = UUID.randomUUID().toString();
    VisitDto visitDto =
        VisitDto.builder()
            .localId(localId)
            .deleted(false)
            //  .cudChewing(CudChewingTool.builder().build())
            .rumenHealth(visitMapper.modelToDtoForRumenHealth(RumenHealthTool.builder().build()))
            .bodyCondition(visitMapper.modelToDtoForBCS(BodyConditionTool.builder().build()))
            .foragePennState(
                visitMapper.modelToDtoForForagePennState(ForagePennStateTool.builder().build()))
            .tmrParticleScore(
                visitMapper.modelToDtoForTmrParticleScore(
                    RumenHealthTMRParticleScoreTool.builder().build()))
            .milkSoldEvaluation(
                visitMapper.modelToDtoForMilkSoldEvaluation(
                    MilkSoldEvaluationTool.builder().build()))
            .rumenHealthManureScore(
                visitMapper.modeltoDtoForRumenHealthManureScore(
                    RumenHealthManureScoreTool.builder().build()))
            .metabolicIncidence(
                visitMapper.modelToDtoForMetabolicIncidence(
                    MetabolicIncidenceTool.builder().build()))
            .roboticMilkEvaluation(
                visitMapper.modelToDtoForRoboticMilkEvaluation(
                    RoboticMilkEvaluationTool.builder().build()))
            .locomotionScore(visitMapper.modelToDtoForLocomotion(LocomotionTool.builder().build()))
            .heatStress(visitMapper.modelToDtoForHeatStress(HeatStressTool.builder().build()))
            .penTimeBudgetTool(
                visitMapper.modelToDtoForPenTimeBudget(PenTimeBudgetTool.builder().build()))
            .manureScreenerTool(
                visitMapper.modelToDtoForManureScreenerTool(ManureScreenerTool.builder().build()))
            .forageAuditScorecard(
                visitMapper.modelToDtoForForageAuditScorecard(Scorecard.builder().build()))
            .profitabilityAnalysisTool(
                visitMapper.modelToDtoForProfitabilityAnalysis(
                    ProfitabilityAnalysisTool.builder().build()))
            .calfHeiferScorecard(
                visitMapper.modelToDtoForCalfHeiferScorecard(CalfHeiferScorecard.builder().build()))
            .returnOverFeedTool(
                visitMapper.modelToDtoForReturnOverFeed(ReturnOverFeedTool.builder().build()))
            .animalAnalysis(
                AnimalAnalysisToolDto.builder()
                    .visitId(UUID.randomUUID())
                    .animals(
                        Arrays.asList(
                            AnimalAnalysisToolItemDto.builder()
                                .penId(UUID.randomUUID())
                                .animalDetails(
                                    Arrays.asList(
                                        AnimalAnalysisDetailsToolItemDto.builder().build()))
                                .build()))
                    .build())
            .visitDate(Instant.now())
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .selectedCurrency(Currencies.CAD)
            .build();
    SiteDocument siteDocument =
        SiteDocument.builder()
            .visits(
                List.of(
                    SiteVisit.builder()
                        .visitDate(Instant.now())
                        .visitName("John doe")
                        .status(VisitStatus.InProgress)
                        .labyrinthVisitId(UUID.randomUUID())
                        .build()))
            .build();

    Sites sites = Sites.builder().siteDocument(siteDocument).build();
    when(sitesRepository.findBySiteId(any())).thenReturn(sites);
    when(visitsRepository.existsByLocalId(any())).thenReturn(false);
    when(visitsRepository.save(any())).thenReturn(loadVisit());

    when(visitMapper.dtoToModelForLocomotion(any()))
        .thenReturn(loadVisit().getVisitDocument().getLocomotionScore());
    when(visitMapper.dtoToModelForRoboticMilkEvaluation(any()))
        .thenReturn(loadVisit().getVisitDocument().getRoboticMilkEvaluation());
    when(visitMapper.dtoToModelForRumenHealth(any()))
        .thenReturn(loadVisit().getVisitDocument().getRumenHealth());
    when(visitMapper.dtoToModelForBCS(any()))
        .thenReturn(loadVisit().getVisitDocument().getBodyCondition());
    when(visitMapper.dtoToModelForAnimalAnalysis(any()))
        .thenReturn(loadVisit().getVisitDocument().getAnimalAnalysis());
    when(visitMapper.dtoToModelForMetabolicIncidence(any()))
        .thenReturn(loadVisit().getVisitDocument().getMetabolicIncidence());
    when(visitMapper.dtoToModelForRumenHealthManureScore(any()))
        .thenReturn(loadVisit().getVisitDocument().getRumenHealthManureScore());
    when(visitMapper.dtoToModelForTmrParticleScore(any()))
        .thenReturn(loadVisit().getVisitDocument().getTmrParticleScore());
    when(visitMapper.dtoToModelForForagePennState(any()))
        .thenReturn(loadVisit().getVisitDocument().getForagePennState());
    when(visitMapper.dtoToModelForHeatStress(any()))
        .thenReturn(loadVisit().getVisitDocument().getHeatStress());
    when(visitMapper.dtoToModelForPenTimeBudget(any()))
        .thenReturn(loadVisit().getVisitDocument().getPenTimeBudgetTool());
    when(visitMapper.dtoToModelForManureScreenerTool(any()))
        .thenReturn(loadVisit().getVisitDocument().getManureScreenerTool());
    when(visitMapper.dtoToModelForForageAuditScorecard(any()))
        .thenReturn(loadVisit().getVisitDocument().getForageAuditScorecard());
    when(visitMapper.dtoToModelForProfitabilityAnalysis(any()))
        .thenReturn(loadVisit().getVisitDocument().getProfitabilityAnalysis());
    when(visitMapper.dtoToModelForCalfHeiferScorecard(any()))
        .thenReturn(loadVisit().getVisitDocument().getCalfHeiferScorecard());
    when(visitMapper.dtoToModelForReturnOverFeed(any()))
        .thenReturn(loadVisit().getVisitDocument().getReturnOverFeedTool());

    VisitDto result = visitService.save(visitDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void WhenAllWorksCorrectSaveResultIsReturnedWithNoTools() throws Exception {
    String localId = UUID.randomUUID().toString();
    VisitDto visitDto =
        VisitDto.builder()
            .localId(localId)
            .visitDate(Instant.now())
            .id(UUID.randomUUID())
            .deleted(false)
            .customerId(UUID.randomUUID())
            .mobileLastUpdatedTime(Instant.now())
            .siteId(UUID.randomUUID())
            .build();
    SiteDocument siteDocument =
        SiteDocument.builder()
            .visits(
                List.of(
                    SiteVisit.builder()
                        .visitDate(Instant.now())
                        .visitName("John doe")
                        .status(VisitStatus.InProgress)
                        .labyrinthVisitId(UUID.randomUUID())
                        .build()))
            .build();

    Sites sites = Sites.builder().siteDocument(siteDocument).build();
    when(sitesRepository.findBySiteId(any())).thenReturn(sites);
    when(visitsRepository.existsByLocalId(any())).thenReturn(false);
    when(visitsRepository.save(any())).thenReturn(loadVisit());
    when(visitMapper.modelToDtoForLocomotion(any())).thenReturn(null);
    when(visitMapper.modelToDtoForRoboticMilkEvaluation(any())).thenReturn(null);
    when(visitMapper.modelToDtoForRumenHealth(any())).thenReturn(null);
    when(visitMapper.modelToDtoForBCS(any())).thenReturn(null);
    when(visitMapper.modelToDtoForAnimalAnalysis(any())).thenReturn(null);
    when(visitMapper.dtoToModelForLocomotion(any())).thenReturn(null);
    when(visitMapper.dtoToModelForRumenHealth(any())).thenReturn(null);
    when(visitMapper.dtoToModelForBCS(any())).thenReturn(null);
    when(visitMapper.dtoToModelForAnimalAnalysis(any())).thenReturn(null);
    when(visitMapper.dtoToModelForMetabolicIncidence(any())).thenReturn(null);
    when(visitMapper.modelToDtoForMetabolicIncidence(any())).thenReturn(null);
    when(visitMapper.modeltoDtoForRumenHealthManureScore(any())).thenReturn(null);
    when(visitMapper.dtoToModelForRumenHealthManureScore(any())).thenReturn(null);
    when(visitMapper.dtoToModelForPenTimeBudget(any())).thenReturn(null);
    when(visitMapper.dtoToModelForManureScreenerTool(any())).thenReturn(null);
    when(visitMapper.dtoToModelForForageAuditScorecard(any())).thenReturn(null);
    when(visitMapper.dtoToModelForProfitabilityAnalysis(any())).thenReturn(null);
    when(visitMapper.dtoToModelForCalfHeiferScorecard(any())).thenReturn(null);
    when(visitMapper.dtoToModelForReturnOverFeed(any())).thenReturn(null);
    VisitDto result = visitService.save(visitDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void WhenVisitIsNotAvailableAddInNotPublishedList() throws Exception {
    when(visitsRepository.findByVisitId(any())).thenReturn(null);

    VisitPublishResponseDto result =
        visitService.autoPublish(loadVisitPublishList(), locale, resourceBundleMessageSource);
    assertEquals(1, result.getNotPublishedVisits().size());
  }

  @Test
  void WhenVisitIsAvailableAddInPublishedList() throws Exception {
    when(visitsRepository.findByVisitId(any())).thenReturn(loadVisit());
    when(activitiesRepository.existByVisitId(any())).thenReturn(false);
    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccount());
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(activitiesRepository.save(any())).thenReturn(loadActivities());

    VisitPublishResponseDto result =
        visitService.autoPublish(loadVisitPublishList(), locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertFalse(result.getPublishedVisits().isEmpty());
  }

  @Test
  void WhenVisitIsAvailableInActivitiesAddInPublishedList() throws Exception {
    when(visitsRepository.findByVisitId(any())).thenReturn(loadVisitWithNoTools());
    when(activitiesRepository.existByVisitId(any())).thenReturn(true);
    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccount());
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());

    VisitPublishResponseDto result =
        visitService.autoPublish(loadVisitPublishList(), locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertFalse(result.getPublishedVisits().isEmpty());
  }

  @Test
  void whenActivityIsCreatedForPublishedVisitsCorrectResultIsReturned() throws Exception {
    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccount());
    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(activitiesRepository.save(any())).thenReturn(loadActivities());
    when(activitiesRepository.findByDocumentIdAndDeleted(any())).thenReturn(loadActivities());
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            .status(VisitStatus.Published)
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .visitDate(Instant.now())
            .visitName("Test Visit")
            .visitPublishedDateTimeUtc(Instant.now())
            .build();
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    assertNotNull(loadActivities().getActivityDocument().getId());
  }

  @Test
  void whenActivityIsCreatedForPublishedVisitsWithSiteMappingCorrectResultIsReturned()
      throws Exception {
    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccountWithBusinessId());
    when(activitiesRepository.save(any())).thenReturn(loadActivities());
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(activitiesRepository.findByDocumentIdAndDeleted(any())).thenReturn(loadActivities());
    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            .status(VisitStatus.Published)
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .visitDate(Instant.now())
            .visitName("Test Visit")
            .visitPublishedDateTimeUtc(Instant.now())
            .build();
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    assertNotNull(loadActivities().getActivityDocument().getId());
  }

  @Test
  void whenVisitDtoHasLessFieldsActivityIsCreatedForPublishedVisitsCorrectResultIsReturned()
      throws Exception {
    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccount());
    when(activitiesRepository.save(any())).thenReturn(loadActivities());
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(activitiesRepository.findByDocumentIdAndDeleted(any())).thenReturn(loadActivities());
    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            .status(VisitStatus.Published)
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .visitName("Test Visit")
            .build();
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    assertNotNull(loadActivities().getActivityDocument().getId());
  }

  @Test
  void whenSaveToLiftIsCalledResultIsCompleted() throws Exception {
    EventDocument eventDocument = EventDocument.builder().id(UUID.randomUUID()).build();
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(activitiesRepository.findByDocumentIdAndDeleted(any())).thenReturn(loadActivities());
    visitService.saveToSf(eventDocument, locale, resourceBundleMessageSource);
    assertNotNull(eventDocument.getId());
  }

  @Test
  void whenSaveToLiftIsCalledExceptionIsThrown() throws Exception {
    EventDocument eventDocument = EventDocument.builder().id(UUID.randomUUID()).build();
    when(activitiesRepository.findByDocumentIdAndDeleted(any())).thenReturn(loadActivities());
    when(activitiesRepository.save(any())).thenReturn(loadActivities());
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(
            PayloadValidationDto.builder()
                .errorDetails(
                    List.of(
                        LiftResponseEntityDto.builder()
                            .message("test")
                            .status(ResponseStatus.FAILED)
                            .build()))
                .build());
    Assertions.assertThrows(
        CustomDEExceptions.class,
        () -> visitService.saveToSf(eventDocument, locale, resourceBundleMessageSource));
  }

  @Test
  void whenActivityIsCreatedForWhatIDNullSiteIdNull() throws Exception {

    AccountDocument accountDocument =
        AccountDocument.builder()
            .id(UUID.randomUUID())
            .dataSource(DataSource.LIFT)
            .goldenRecordId(null)
            .contacts(
                List.of(
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .sFDCContactId("sf-1")
                        .firstName("John")
                        .lastName("Doe")
                        .build(),
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("Jane")
                        .lastName("Doe")
                        .build()))
            .physicalAddress(Address.builder().build())
            .build();
    Accounts accounts = Accounts.builder().accountDocument(accountDocument).build();
    when(accountsRepository.findByAccountId(any())).thenReturn(accounts);
    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(sitesRepository.findBySiteId(any())).thenReturn(loadSites());
    when(activitiesRepository.save(any())).thenReturn(loadActivities());

    VisitDto visitDto =
        VisitDto.builder()
            .id(UUID.randomUUID())
            .status(VisitStatus.Published)
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .visitDate(Instant.now())
            .visitName("Test Visit")
            .visitPublishedDateTimeUtc(Instant.now())
            .build();
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    assertNotNull(loadActivities().getActivityDocument().getId());
  }

  private List<VisitPublishDto> loadVisitPublishList() {
    VisitPublishDto visitPublishDto =
        VisitPublishDto.builder()
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .visitId(UUID.randomUUID())
            .build();
    List<VisitPublishDto> visitPublishDtoList = new ArrayList<>();
    visitPublishDtoList.add(visitPublishDto);
    return visitPublishDtoList;
  }

  private Visits loadVisit() {

    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
            .siteId(UUID.fromString("********-9ea9-475a-a0fc-39dc8357d44"))
            .status(VisitStatus.InProgress)
            .rumenHealth(
                RumenHealthTool.builder()
                    .pens(
                        List.of(
                            RumenHealthToolItem.builder()
                                .cudChewingCowsCount(
                                    CudChewingCowCount.builder()
                                        .countNo(5)
                                        .countYes(10)
                                        .noPercent(50.5)
                                        .yesPercent(60.5)
                                        .totalCount(0.0)
                                        .build())
                                .cudChewsCount(
                                    List.of(
                                        CudChewingCount.builder()
                                            .chewsCount(10)
                                            .cowNumber(10)
                                            .build()))
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .build()))
                    .visitId(UUID.randomUUID())
                    .goals(
                        List.of(
                            HerdAnalysisGoal.builder()
                                .cudChews(3.6)
                                .percentChewing(50.6)
                                .stage(LactationStage.FarOffDry)
                                .build()))
                    .build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .rumenFillManureScore(RumenFillTool.builder().build())
            .cudChewing(
                CudChewingTool.builder()
                    .createTimeUtc(Instant.now())
                    .createUser("admin@admin")
                    .cudChewingReports(
                        List.of(
                            CudChewingByPen.builder()
                                .countNo(5)
                                .countYes(5)
                                .noPercent(0.0)
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .yesPercent(0.0)
                                .build()))
                    .build())
            .animalAnalysis(
                AnimalAnalysisTool.builder()
                    .visitId(UUID.randomUUID())
                    .animals(
                        Arrays.asList(
                            AnimalAnalysisToolItem.builder()
                                .penId(UUID.randomUUID())
                                .animalDetails(
                                    Arrays.asList(
                                        AnimalAnalysisDetailsToolItem.builder()
                                            .locomotionScore(1.0)
                                            .build()))
                                .build()))
                    .build())
            .metabolicIncidence(MetabolicIncidenceTool.builder().build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(BodyConditionTool.builder().build())
            .roboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build())
            .rumenHealthManureScore(RumenHealthManureScoreTool.builder().build())
            .tmrParticleScore(RumenHealthTMRParticleScoreTool.builder().build())
            .foragePennState(ForagePennStateTool.builder().build())
            .penTimeBudgetTool(PenTimeBudgetTool.builder().build())
            .heatStress(HeatStressTool.builder().build())
            .manureScreenerTool(ManureScreenerTool.builder().build())
            .forageAuditScorecard(Scorecard.builder().build())
            .profitabilityAnalysis(ProfitabilityAnalysisTool.builder().build())
            .calfHeiferScorecard(CalfHeiferScorecard.builder().build())
            .returnOverFeedTool(ReturnOverFeedTool.builder().build())
            .visitDate(Instant.parse("2022-10-05T09:50:03.028Z"))
            .visitName("John Doe 2022-09-29T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    return Visits.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(visitDocument.getId().toString())
        .visitDocument(visitDocument)
        .build();
  }

  private Visits loadVisitWithNoTools() {

    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
            .siteId(UUID.fromString("********-9ea9-475a-a0fc-39dc8357d44"))
            .status(VisitStatus.InProgress)
            .visitDate(Instant.parse("2022-10-05T09:50:03.028Z"))
            .visitName("John Doe 2022-09-29T08:56:34.390Z")
            .build();

    return Visits.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(visitDocument.getId().toString())
        .visitDocument(visitDocument)
        .build();
  }

  private Accounts loadAccount() {
    AccountDocument accountDocument =
        AccountDocument.builder()
            .id(UUID.randomUUID())
            .dataSource(DataSource.LIFT)
            .goldenRecordId(UUID.randomUUID().toString())
            .contacts(
                List.of(
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .sFDCContactId("sf-1")
                        .firstName("John")
                        .lastName("Doe")
                        .build(),
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("Jane")
                        .lastName("Doe")
                        .build()))
            .physicalAddress(Address.builder().build())
            .build();
    return Accounts.builder()
        .accountDocument(accountDocument)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  private Accounts loadAccountWithBusinessId() {
    AccountDocument accountDocument =
        AccountDocument.builder()
            .id(UUID.randomUUID())
            .dataSource(DataSource.LIFT)
            .users(Set.of("user1", "user2"))
            .contacts(
                List.of(
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .sFDCContactId("sf-1")
                        .firstName("John")
                        .lastName("Doe")
                        .build(),
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("Jane")
                        .lastName("Doe")
                        .build()))
            .physicalAddress(Address.builder().build())
            .businessID(1)
            .goldenRecordId(UUID.randomUUID().toString())
            .build();
    return Accounts.builder()
        .accountDocument(accountDocument)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  private Sites loadSites() {
    SiteDocument siteDocument =
        SiteDocument.builder()
            .id(UUID.randomUUID())
            .visits(
                List.of(
                    SiteVisit.builder()
                        .visitDate(Instant.now())
                        .visitName("John doe")
                        .status(VisitStatus.Published)
                        .labyrinthVisitId(UUID.randomUUID())
                        .build()))
            .externalId(UUID.randomUUID().toString())
            .build();
    return Sites.builder()
        .siteDocument(siteDocument)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  private Activities loadActivities() {
    ActivityDocument activityDocument =
        ActivityDocument.builder()
            .id(UUID.randomUUID())
            .accountId(UUID.randomUUID())
            .activityDateTime(Instant.now())
            .build();
    return Activities.builder()
        .activityDocument(activityDocument)
        .createdDate(Date.from(Instant.now()))
        .createdDate(Date.from(Instant.now()))
        .build();
  }

  private VisitDto loadVisitDto() {
    return VisitDto.builder()
        .id(UUID.randomUUID())
        .customerId(UUID.randomUUID())
        .siteId(UUID.randomUUID())
        .visitDate(Instant.now())
        .status(VisitStatus.InProgress)
        .visitName("Test Visit")
        .firstName("Test")
        .lastName("User")
        .isVisitAutoPublished(false)
        .needsSync(false)
        .isNew(true)
        .createUser("<EMAIL>")
        .lastModifyUser("<EMAIL>")
        .build();
  }

  // Test cases for Crescendo integration functionality

  @Test
  void testSaveToCrescendo_Success() throws Exception {
    // Arrange
    Accounts account = loadAccount();
    account.getAccountDocument().setGoldenRecordId("test-golden-record-id");
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);

    Sites site = loadSites();
    VisitDto visitDto = loadVisitDto();
    Activities activity = loadActivities();
    activity.getActivityDocument().setSubject("Test Activity");
    activity.getActivityDocument().setActivityDateTime(Instant.now());
    activity.getActivityDocument().setEndDateTime(Instant.now().plusSeconds(3600));
    activity.getActivityDocument().setSfdcAccountId("test-sfdc-account-id");

    Users crescendoUser = new Users();
    crescendoUser.setId("test-user-id");
    crescendoUser.setEmail("<EMAIL>");

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(crescendoUser));

    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(crescendoActivityService.createActivity(
            any(EventsCrescendo.class), eq(locale), eq(resourceBundleMessageSource)))
        .thenReturn("test-event-id");
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);
    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);

    // Act
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    // Assert
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoActivityService)
        .createActivity(any(EventsCrescendo.class), eq(locale), eq(resourceBundleMessageSource));
    verify(activitiesRepository, times(2)).save(any(Activities.class));
  }

  @Test
  void testSaveToCrescendo_AccountNotSyncedToCrescendo() throws Exception {
    // Arrange
    Accounts account = loadAccount();
    account.getAccountDocument().setGoldenRecordId(null); // Account not synced
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);

    Sites site = loadSites();
    VisitDto visitDto = loadVisitDto();
    Activities activity = loadActivities();

    Users crescendoUser = new Users();
    crescendoUser.setId("test-user-id");

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(crescendoUser));

    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    // Assert - Should not call crescendoActivityService when account is not synced
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoActivityService, never())
        .createActivity(
            any(EventsCrescendo.class), any(Locale.class), any(ResourceBundleMessageSource.class));
    verify(activitiesRepository).save(any(Activities.class));
  }

  @Test
  void testSaveToCrescendo_DateRangeOlderThan14Days() throws Exception {
    // Arrange
    Accounts account = loadAccount();
    account.getAccountDocument().setGoldenRecordId("test-golden-record-id");
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);

    Sites site = loadSites();
    VisitDto visitDto = loadVisitDto();
    Activities activity = loadActivities();
    // Set date range with duration greater than 14 days (this should fail validation)
    Instant currentTime = Instant.now();
    Instant startTime = currentTime.minusSeconds(20 * 24 * 60 * 60); // 20 days ago
    Instant endTime = currentTime; // Now (duration = 20 days > 14 days)
    activity.getActivityDocument().setActivityDateTime(startTime);
    activity.getActivityDocument().setEndDateTime(endTime);

    Users crescendoUser = new Users();
    crescendoUser.setId("test-user-id");

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(crescendoUser));

    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    // Assert - Should not call crescendoActivityService when date validation fails (older than 14
    // days)
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoActivityService, never())
        .createActivity(
            any(EventsCrescendo.class), any(Locale.class), any(ResourceBundleMessageSource.class));
    verify(activitiesRepository).save(any(Activities.class));
  }

  @Test
  void testSaveToCrescendo_CrescendoServiceException() throws Exception {
    // Arrange
    Accounts account = loadAccount();
    account.getAccountDocument().setGoldenRecordId("test-golden-record-id");
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);

    Sites site = loadSites();
    VisitDto visitDto = loadVisitDto();
    Activities activity = loadActivities();
    activity.getActivityDocument().setActivityDateTime(Instant.now());
    activity.getActivityDocument().setEndDateTime(Instant.now().plusSeconds(3600));

    Users crescendoUser = new Users();
    crescendoUser.setId("test-user-id");

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(crescendoUser));

    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(crescendoActivityService.createActivity(
            any(EventsCrescendo.class), eq(locale), eq(resourceBundleMessageSource)))
        .thenThrow(new CustomDEExceptions("Crescendo service error"));
    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act & Assert
    Assertions.assertThrows(
        CustomDEExceptions.class,
        () -> visitService.createActivity(visitDto, locale, resourceBundleMessageSource));

    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoActivityService)
        .createActivity(any(EventsCrescendo.class), eq(locale), eq(resourceBundleMessageSource));
  }

  @Test
  void testMapActivityToCrescendoEvent_Success() throws Exception {
    // Arrange
    Activities activity = loadActivities();
    activity.getActivityDocument().setSubject("Test Activity Subject");
    activity.getActivityDocument().setActivityDateTime(Instant.parse("2025-07-31T10:00:00Z"));
    activity.getActivityDocument().setEndDateTime(Instant.parse("2025-07-31T11:00:00Z"));
    activity.getActivityDocument().setSfdcAccountId("test-sfdc-account-id");

    Accounts account = loadAccount();
    account.getAccountDocument().setId(UUID.fromString("********-1234-1234-1234-********9012"));

    Sites site = loadSites();
    site.getSiteDocument().setId(UUID.fromString("*************-4321-4321-************"));

    Users user = new Users();
    user.setId("test-user-id");
    user.setEmail("<EMAIL>");

    VisitDto visitDto = loadVisitDto();

    // Act - Use reflection to call the private method
    try {
      java.lang.reflect.Method method =
          VisitServiceImpl.class.getDeclaredMethod(
              "mapActivityToCrescendoEvent",
              Activities.class,
              Accounts.class,
              Sites.class,
              Users.class,
              VisitDto.class);
      method.setAccessible(true);
      EventsCrescendo result =
          (EventsCrescendo) method.invoke(visitService, activity, account, site, user, visitDto);

      // Assert
      assertNotNull(result);
      assertEquals("Test Activity Subject", result.getSubject());
      assertEquals("2025-07-31T10:00:00Z", result.getStartDateTime());
      assertEquals("2025-07-31T11:00:00Z", result.getEndDateTime());
      assertEquals("test-sfdc-account-id", result.getWhatId());
      assertEquals("test-user-id", result.getOwnerId());
      assertEquals("********-1234-1234-1234-********9012", result.getExternalAccountId());
      assertEquals("*************-4321-4321-************", result.getExternalId());
      assertEquals(true, result.getMobileFirst());

    } catch (Exception e) {
      Assertions.fail("Failed to invoke mapActivityToCrescendoEvent method: " + e.getMessage());
    }
  }

  @Test
  void testMapActivityToCrescendoEvent_WithNullValues() throws Exception {
    // Arrange
    Activities activity = loadActivities();
    activity.getActivityDocument().setSubject("Test Activity");
    activity.getActivityDocument().setActivityDateTime(null);
    activity.getActivityDocument().setEndDateTime(null);
    activity.getActivityDocument().setSfdcAccountId(null);

    Accounts account = loadAccount();
    Sites site = loadSites();
    Users user = new Users();
    user.setId("test-user-id");

    VisitDto visitDto = loadVisitDto();

    // Act - Use reflection to call the private method
    try {
      java.lang.reflect.Method method =
          VisitServiceImpl.class.getDeclaredMethod(
              "mapActivityToCrescendoEvent",
              Activities.class,
              Accounts.class,
              Sites.class,
              Users.class,
              VisitDto.class);
      method.setAccessible(true);
      EventsCrescendo result =
          (EventsCrescendo) method.invoke(visitService, activity, account, site, user, visitDto);

      // Assert
      assertNotNull(result);
      assertEquals("Test Activity", result.getSubject());
      assertEquals(null, result.getStartDateTime());
      assertEquals(null, result.getEndDateTime());
      assertEquals(null, result.getWhatId());
      assertEquals("test-user-id", result.getOwnerId());
      assertEquals(true, result.getMobileFirst());

    } catch (Exception e) {
      Assertions.fail("Failed to invoke mapActivityToCrescendoEvent method: " + e.getMessage());
    }
  }

  @Test
  void testCreateActivity_WithCrescendoDataSource() throws Exception {
    // Arrange
    VisitDto visitDto = loadVisitDto();
    visitDto.setStatus(VisitStatus.Published);

    Accounts account = loadAccount();
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);
    account.getAccountDocument().setGoldenRecordId("test-golden-record-id");

    Sites site = loadSites();
    Activities activity = loadActivities();
    activity.getActivityDocument().setActivityDateTime(Instant.now());
    activity.getActivityDocument().setEndDateTime(Instant.now().plusSeconds(3600));

    Users crescendoUser = new Users();
    crescendoUser.setId("test-user-id");

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(crescendoUser));

    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(crescendoActivityService.createActivity(
            any(EventsCrescendo.class), eq(locale), eq(resourceBundleMessageSource)))
        .thenReturn("test-event-id");
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    // Assert
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoActivityService)
        .createActivity(any(EventsCrescendo.class), eq(locale), eq(resourceBundleMessageSource));
    verify(activitiesRepository, times(2)).save(any(Activities.class));
  }

  @Test
  void testCreateActivity_WithLiftDataSource() throws Exception {
    // Arrange
    VisitDto visitDto = loadVisitDto();
    visitDto.setStatus(VisitStatus.Published);

    Accounts account = loadAccount();
    account.getAccountDocument().setDataSource(DataSource.LIFT);
    account.getAccountDocument().setGoldenRecordId("test-golden-record-id");

    Sites site = loadSites();
    site.getSiteDocument().setExternalId("test-external-id");

    Activities activity = loadActivities();
    User liftUser = new User();
    liftUser.setId("test-lift-user-id");

    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(liftUserService.findOwner("<EMAIL>")).thenReturn(liftUser);
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);
    when(activitiesRepository.findByDocumentIdAndDeleted(any())).thenReturn(activity);

    // Act
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    // Assert
    verify(liftUserService).findOwner("<EMAIL>");
    verify(liftApiService).validate(any(), any(), any(), any());
    verify(activitiesRepository, times(2)).save(any(Activities.class));
    // Should not call Crescendo services
    verify(crescendoUserService, never()).getByUserEmail(any());
    verify(crescendoActivityService, never()).createActivity(any(), any(), any());
  }

  @Test
  void testCreateActivity_WithUnknownDataSource() throws Exception {
    // Arrange
    VisitDto visitDto = loadVisitDto();
    visitDto.setStatus(VisitStatus.Published);

    Accounts account = loadAccount();
    account.getAccountDocument().setDataSource(DataSource.UNKNOWN);

    Sites site = loadSites();
    Activities activity = loadActivities();

    Users crescendoUser = new Users();
    crescendoUser.setId("test-user-id");

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(crescendoUser));

    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    // Assert - Should use Crescendo path for non-LIFT data sources
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(activitiesRepository, times(2))
        .save(
            any(Activities.class)); // Called twice: once in createActivity, once in saveToCrescendo
    // Should not call LIFT services
    verify(liftUserService, never()).findOwner(any());
    verify(liftApiService, never()).validate(any(), any(), any(), any());
  }

  @Test
  void testCreateActivity_CrescendoUserServiceException() throws Exception {
    // Arrange
    VisitDto visitDto = loadVisitDto();
    visitDto.setStatus(VisitStatus.Published);

    Accounts account = loadAccount();
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);
    account.getAccountDocument().setGoldenRecordId("test-golden-record-id");

    Sites site = loadSites();
    Activities activity = loadActivities();

    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>"))
        .thenThrow(new RuntimeException("User service error"));
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act & Assert
    Assertions.assertThrows(
        RuntimeException.class,
        () -> visitService.createActivity(visitDto, locale, resourceBundleMessageSource));

    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(activitiesRepository).save(any(Activities.class));
  }

  @Test
  void testCreateActivity_EmptyUserRecords() throws Exception {
    // Arrange
    VisitDto visitDto = loadVisitDto();
    visitDto.setStatus(VisitStatus.Published);

    Accounts account = loadAccount();
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);
    account.getAccountDocument().setGoldenRecordId("test-golden-record-id");

    Sites site = loadSites();
    Activities activity = loadActivities();

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of()); // Empty records

    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act & Assert - Should throw IndexOutOfBoundsException when trying to access first record
    Assertions.assertThrows(
        IndexOutOfBoundsException.class,
        () -> visitService.createActivity(visitDto, locale, resourceBundleMessageSource));

    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(activitiesRepository).save(any(Activities.class));
  }

  @Test
  void testCreateActivity_ValidDateRangeButNullGoldenRecordId() throws Exception {
    // Arrange
    VisitDto visitDto = loadVisitDto();
    visitDto.setStatus(VisitStatus.Published);

    Accounts account = loadAccount();
    account.getAccountDocument().setDataSource(DataSource.CRESCENDO);
    account.getAccountDocument().setGoldenRecordId(null); // Null golden record ID

    Sites site = loadSites();
    Activities activity = loadActivities();
    activity.getActivityDocument().setActivityDateTime(Instant.now());
    activity.getActivityDocument().setEndDateTime(Instant.now().plusSeconds(3600));

    Users crescendoUser = new Users();
    crescendoUser.setId("test-user-id");

    SalesforceRecordsResponse<Users> userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(crescendoUser));

    when(accountsRepository.findByAccountId(any())).thenReturn(account);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(crescendoUserService.getByUserEmail("<EMAIL>")).thenReturn(userResponse);
    when(activitiesRepository.save(any(Activities.class))).thenReturn(activity);

    // Act
    visitService.createActivity(visitDto, locale, resourceBundleMessageSource);

    // Assert - Should not create activity in Crescendo when golden record ID is null
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoActivityService, never())
        .createActivity(
            any(EventsCrescendo.class), any(Locale.class), any(ResourceBundleMessageSource.class));
    verify(activitiesRepository).save(any(Activities.class));
  }
}
