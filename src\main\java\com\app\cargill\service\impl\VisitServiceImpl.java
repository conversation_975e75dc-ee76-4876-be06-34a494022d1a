/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.ActivityType;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.NameType;
import com.app.cargill.constants.RelatedToType;
import com.app.cargill.constants.SearchKey;
import com.app.cargill.constants.SearchOperation;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.crescendo.model.EventsCrescendo;
import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoActivityService;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.EventDocument;
import com.app.cargill.document.ReportType;
import com.app.cargill.document.SiteVisit;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.ISelectDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.dto.VisitDto;
import com.app.cargill.dto.VisitPublishDto;
import com.app.cargill.dto.VisitPublishResponseDto;
import com.app.cargill.dto.VisitSearchDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.filterspecification.FilterSpecification;
import com.app.cargill.filterspecification.SearchCriteria;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Activities;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.IVisitService;
import com.app.cargill.service.impl.mappers.VisitMapper;
import com.app.cargill.sf.cc.constants.LiftEntityName;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.EventUpdateModel;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftEventService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.app.cargill.sf.cc.utils.LiftUtils;
import com.app.cargill.utils.JsonUtils;
import com.app.cargill.utils.PageableUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class VisitServiceImpl implements IVisitService {

  private final VisitsRepository visitsRepository;

  private final IUserService userServiceImpl;

  private final ActivitiesRepository activitiesRepository;

  private final AccountsRepository accountsRepository;

  private final SitesRepository sitesRepository;

  private final VisitMapper visitMapper;

  private Map<String, String> siteNames = new LinkedHashMap<>();

  private Map<String, String> accountNames = new LinkedHashMap<>();

  private final LiftUserService liftUserService;
  private final LiftApiService liftApiService;

  private final LiftEventService liftEventServiceImpl;
  private final LiftUtils liftUtils;

  private static final String EVENT = "Event";
  private final ICrescendoActivityService crescendoActivityService;
  private final ICrescendoUserService crescendoUserService;

  @Value("${app.configurations.default-utc-timestamp}")
  Instant minInstant;

  @Override
  public List<VisitDto> fetchAllVisits() {
    List<Visits> visits = visitsRepository.findAll();

    if (visits.isEmpty()) {
      throw new NotFoundDEException("No visits found");
    }

    return visits.stream().map(this::mapToDto).toList();
  }

  VisitDto mapToDto(Visits visit) {
    return VisitDto.builder()
        .createdDate(visit.getCreatedDate().toInstant())
        .customerId(visit.getVisitDocument().getCustomerId())
        .deleted(visit.isDeleted())
        .usedPens(visit.getVisitDocument().getPensUsed())
        .unitOfMeasure(visit.getVisitDocument().getUnitOfMeasure())
        .createUser(visit.getVisitDocument().getCreateUser())
        .lastModifyUser(visit.getVisitDocument().getLastModifyUser())
        .firstName(visit.getVisitDocument().getFirstName())
        .formattedCreationDate(visit.getVisitDocument().getFormattedCreationDate())
        .id(visit.getVisitDocument().getId())
        .isVisitAutoPublished(visit.getVisitDocument().getIsVisitAutoPublished())
        .selectedPointScale(visit.getVisitDocument().getSelectedPointScale())
        .lastName(visit.getVisitDocument().getLastName())
        .localId(visit.getLocalId())
        .needsSync(visit.getVisitDocument().getNeedsSync())
        .tmrParticleScore(
            visitMapper.modelToDtoForTmrParticleScore(
                visit.getVisitDocument().getTmrParticleScore()))
        .rumenHealth(
            visitMapper.modelToDtoForRumenHealth(visit.getVisitDocument().getRumenHealth()))
        //
        // .cudChewing(visit.getVisitDocument().getCudChewing()!=null?visit.getVisitDocument().getCudChewing():null)
        .selected(visit.getVisitDocument().getSelected())
        .metabolicIncidence(
            visitMapper.modelToDtoForMetabolicIncidence(
                visit.getVisitDocument().getMetabolicIncidence()))
        .milkSoldEvaluation(
            visitMapper.modelToDtoForMilkSoldEvaluation(
                visit.getVisitDocument().getMilkSoldEvaluation()))
        .rumenHealthManureScore(
            visitMapper.modeltoDtoForRumenHealthManureScore(
                visit.getVisitDocument().getRumenHealthManureScore()))
        .pileAndBunker(
            visitMapper.modelToDtoForPileAndBunker(
                visit.getVisitDocument().getPileAndBunker())) // Forage inventories
        .rumenFillManureScore(
            visitMapper.modelToDtoForRumenFillManureScore(
                visit.getVisitDocument().getRumenFillManureScore()))
        .selectedCurrency(
            visit.getVisitDocument().getSelectedCurrency() != null
                ? visit.getVisitDocument().getSelectedCurrency()
                : Currencies.NotSet)
        .siteId(visit.getVisitDocument().getSiteId())
        .status(visit.getVisitDocument().getStatus())
        .updatedDate(visit.getUpdatedDate().toInstant())
        .visitDate(visit.getVisitDocument().getVisitDate())
        .heatStress(visitMapper.modelToDtoForHeatStress(visit.getVisitDocument().getHeatStress()))
        .penTimeBudgetTool(
            visitMapper.modelToDtoForPenTimeBudget(visit.getVisitDocument().getPenTimeBudgetTool()))
        .foragePennState(
            visitMapper.modelToDtoForForagePennState(visit.getVisitDocument().getForagePennState()))
        .forageAuditScorecard(
            visitMapper.modelToDtoForForageAuditScorecard(
                visit.getVisitDocument().getForageAuditScorecard()))
        .siteName(
            siteNames != null
                ? siteNames.get(visit.getVisitDocument().getSiteId().toString())
                : null)
        .accountName(
            accountNames != null
                ? accountNames.get(visit.getVisitDocument().getCustomerId().toString())
                : null)
        .toolsUsed(getToolCount(visit))
        .visitName(visit.getVisitDocument().getVisitName())
        .visitPublishedDateTimeUtc(visit.getVisitDocument().getVisitPublishedDateTimeUtc())
        .bodyCondition(visitMapper.modelToDtoForBCS(visit.getVisitDocument().getBodyCondition()))
        .locomotionScore(
            visitMapper.modelToDtoForLocomotion(visit.getVisitDocument().getLocomotionScore()))
        .roboticMilkEvaluation(
            visitMapper.modelToDtoForRoboticMilkEvaluation(
                visit.getVisitDocument().getRoboticMilkEvaluation()))
        .manureScreenerTool(
            visitMapper.modelToDtoForManureScreenerTool(
                visit.getVisitDocument().getManureScreenerTool()))
        .animalAnalysis(
            visitMapper.modelToDtoForAnimalAnalysis(visit.getVisitDocument().getAnimalAnalysis()))
        .profitabilityAnalysisTool(
            visitMapper.modelToDtoForProfitabilityAnalysis(
                visit.getVisitDocument().getProfitabilityAnalysis()))
        .mobileLastUpdatedTime(visit.getVisitDocument().getMobileLastUpdatedTime())
        .calfHeiferScorecard(
            visitMapper.modelToDtoForCalfHeiferScorecard(
                visit.getVisitDocument().getCalfHeiferScorecard()))
        .returnOverFeedTool(
            visitMapper.modelToDtoForReturnOverFeed(
                visit.getVisitDocument().getReturnOverFeedTool()))
        .currentTimeStamp(Instant.now())
        .build();
  }

  private Integer getToolCount(Visits visit) {
    Integer count = 0;

    if (visit.getVisitDocument().getRumenHealth() != null) {
      count++;
    }
    if (visit.getVisitDocument().getLocomotionScore() != null) {
      count++;
    }
    if (visit.getVisitDocument().getBodyCondition() != null) {
      count++;
    }
    if (visit.getVisitDocument().getAnimalAnalysis() != null) {
      count++;
    }
    if (visit.getVisitDocument().getMilkSoldEvaluation() != null) {
      count++;
    }
    if (visit.getVisitDocument().getMetabolicIncidence() != null) {
      count++;
    }
    if (visit.getVisitDocument().getRoboticMilkEvaluation() != null) {
      count++;
    }
    if (visit.getVisitDocument().getPenTimeBudgetTool() != null) {
      count++;
    }
    if (visit.getVisitDocument().getForagePennState() != null) {
      count++;
    }
    if (visit.getVisitDocument().getTmrParticleScore() != null) {
      count++;
    }
    if (visit.getVisitDocument().getRumenFillManureScore() != null) {
      count++;
    }
    if (visit.getVisitDocument().getRumenHealthManureScore() != null) {
      count++;
    }
    if (visit.getVisitDocument().getPileAndBunker() != null) {
      count++;
    }
    if (visit.getVisitDocument().getHeatStress() != null) {
      count++;
    }
    if (visit.getVisitDocument().getManureScreenerTool() != null) {
      count++;
    }
    if (visit.getVisitDocument().getForageAuditScorecard() != null) {
      count++;
    }
    if (visit.getVisitDocument().getProfitabilityAnalysis() != null) {
      count++;
    }
    if (visit.getVisitDocument().getCalfHeiferScorecard() != null) {
      count++;
    }
    if (visit.getVisitDocument().getReturnOverFeedTool() != null) {
      count++;
    }
    return count;
  }

  @Override
  public Page<VisitDto> getAllVisitsByCurrentLoggedInUser(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting) {

    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
    List<String> visitIds = sitesRepository.findVisitidsByAccountIds(accountIdsByUser);
    visitIds = visitIds.stream().filter(Objects::nonNull).toList();
    Page<Visits> visits =
        visitsRepository.findByVisitIdsOfCurrentLoggedInUser(visitIds, lastSyncTime, pageable);
    if (Objects.isNull(visits)) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        visits.stream().map(this::mapToDto).toList(), pageable, visits.getTotalElements());
  }

  @Override
  public VisitDto save(
      VisitDto visitDto, Locale locale, ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUser();
    if (Objects.isNull(visitDto.getLocalId())) {
      throw new NotFoundDEException("LocalId is null");
    }
    if (visitsRepository.existsByLocalId(visitDto.getLocalId())) {
      throw new AlreadyExistsDEException("Local Id Already exists. Visit Already synced");
    }
    visitDto.setId(UUID.randomUUID());
    if (Objects.equals(visitDto.getStatus(), VisitStatus.Published)
        && !activitiesRepository.existByVisitId(visitDto.getId().toString())) {
      createActivity(visitDto, locale, resourceBundleMessageSource);
    }
    Visits visit = mapToModel(visitDto);
    visitMapper.updateDefaultAttributesForRumenHealthCudChewing(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForBCS(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForLocomotionScore(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForAnimalAnalysis(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForMetabolicIncidence(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForMilkSoldEvaluation(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForRoboticMilkEvaluation(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForRumenHealthManureScore(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForPileAndBunker(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForRumenFillManureScore(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForTmrParticleScore(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForForagePennState(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForPenTimeBudget(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForHeatStress(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForManureScreenerTool(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForForageAuditScorecard(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForProfatibilityAnalysis(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForCalfHeiferScorecard(
        visit, currentLoggedInUser, currentLoggedInUser, false);
    visitMapper.updateDefaultAttributesForReturnOverFeed(
        visit, currentLoggedInUser, currentLoggedInUser, false);

    insertIntoSiteVisitsList(visit);
    visit.getVisitDocument().setLastModifiedTimeUtc(Instant.now());

    return mapToDto(visitsRepository.save(visit));
  }

  private void insertIntoSiteVisitsList(Visits visit) throws NotFoundDEException {
    if (visit.getVisitDocument().getSiteId() == null) {
      throw new NotFoundDEException("Site ID for visit is NULL");
    }
    Sites site = sitesRepository.findBySiteId(visit.getVisitDocument().getSiteId().toString());

    if (site == null) {
      throw new NotFoundDEException(
          "No site found against Site ID:" + visit.getVisitDocument().getSiteId());
    }
    SiteVisit siteVisit =
        SiteVisit.builder()
            .labyrinthVisitId(visit.getVisitDocument().getId())
            .status(visit.getVisitDocument().getStatus())
            .visitDate(visit.getVisitDocument().getVisitDate())
            .visitName(visit.getVisitDocument().getVisitName())
            .build();

    List<SiteVisit> siteVisitList = new ArrayList<>();
    if (!Objects.isNull(site.getSiteDocument().getVisits())) {
      siteVisitList.addAll(site.getSiteDocument().getVisits());
    }
    siteVisitList.add(siteVisit);

    site.getSiteDocument().setVisits(siteVisitList);

    sitesRepository.save(site);
  }

  private Visits mapToModel(VisitDto visitDto) {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(visitDto.getId() != null ? visitDto.getId() : UUID.randomUUID())
            .createTimeUtc(
                visitDto.getCreatedDate() != null ? visitDto.getCreatedDate() : Instant.now())
            .createUser(
                visitDto.getCreateUser() != null
                    ? visitDto.getCreateUser()
                    : userServiceImpl.getCurrentLoggedInUser())
            .customerId(visitDto.getCustomerId())
            .firstName(visitDto.getFirstName())
            .isDeleted(visitDto.isDeleted())
            .unitOfMeasure(visitDto.getUnitOfMeasure())
            .isNew(visitDto.isNew())
            .isVisitAutoPublished(visitDto.getIsVisitAutoPublished())
            .lastModifiedTimeUtc(Instant.now())
            .lastModifyUser(userServiceImpl.getCurrentLoggedInUser())
            .lastName(visitDto.getLastName())
            .mobileLastUpdatedTime(visitDto.getMobileLastUpdatedTime())
            .selectedPointScale(visitDto.getSelectedPointScale())
            .lastSyncTimeUtc(Instant.now())
            .needsSync(true)
            .selectedCurrency(visitDto.getSelectedCurrency())
            .milkSoldEvaluation(
                visitMapper.dtoToModelForMilkSoldEvaluation(visitDto.getMilkSoldEvaluation()))
            .pileAndBunker(visitMapper.dtoToModelForPileAndBunker(visitDto.getPileAndBunker()))
            .rumenFillManureScore(
                visitMapper.dtoToModelForRumenFillManureScore(visitDto.getRumenFillManureScore()))
            .selected(visitDto.getSelected())
            .siteId(visitDto.getSiteId())
            .status(visitDto.getStatus())
            .rumenHealth(visitMapper.dtoToModelForRumenHealth(visitDto.getRumenHealth()))
            .rumenHealthManureScore(
                visitMapper.dtoToModelForRumenHealthManureScore(
                    visitDto.getRumenHealthManureScore()))
            .visitDate(visitDto.getVisitDate() != null ? visitDto.getVisitDate() : Instant.now())
            .foragePennState(
                visitMapper.dtoToModelForForagePennState(visitDto.getForagePennState()))
            .visitName(visitDto.getVisitName())
            .pensUsed(visitDto.getUsedPens())
            .visitPublishedDateTimeUtc(
                visitDto.getVisitPublishedDateTimeUtc() != null
                    ? visitDto.getVisitPublishedDateTimeUtc()
                    : null)
            .formattedCreationDate(
                visitDto.getFormattedCreationDate() != null
                    ? visitDto.getFormattedCreationDate()
                    : Instant.now().toString())
            .bodyCondition(visitMapper.dtoToModelForBCS(visitDto.getBodyCondition()))
            .locomotionScore(visitMapper.dtoToModelForLocomotion(visitDto.getLocomotionScore()))
            .animalAnalysis(visitMapper.dtoToModelForAnimalAnalysis(visitDto.getAnimalAnalysis()))
            .metabolicIncidence(
                visitMapper.dtoToModelForMetabolicIncidence(visitDto.getMetabolicIncidence()))
            .tmrParticleScore(
                visitMapper.dtoToModelForTmrParticleScore(visitDto.getTmrParticleScore()))
            .roboticMilkEvaluation(
                visitMapper.dtoToModelForRoboticMilkEvaluation(visitDto.getRoboticMilkEvaluation()))
            .heatStress(visitMapper.dtoToModelForHeatStress(visitDto.getHeatStress()))
            .penTimeBudgetTool(
                visitMapper.dtoToModelForPenTimeBudget(visitDto.getPenTimeBudgetTool()))
            .manureScreenerTool(
                visitMapper.dtoToModelForManureScreenerTool(visitDto.getManureScreenerTool()))
            .forageAuditScorecard(
                visitMapper.dtoToModelForForageAuditScorecard(visitDto.getForageAuditScorecard()))
            .profitabilityAnalysis(
                visitMapper.dtoToModelForProfitabilityAnalysis(
                    visitDto.getProfitabilityAnalysisTool()))
            .calfHeiferScorecard(
                visitMapper.dtoToModelForCalfHeiferScorecard(visitDto.getCalfHeiferScorecard()))
            .returnOverFeedTool(
                visitMapper.dtoToModelForReturnOverFeed(visitDto.getReturnOverFeedTool()))
            .build();

    return Visits.builder()
        .deleted(visitDto.isDeleted())
        .localId(visitDto.getLocalId())
        .visitDocument(visitDocument)
        .build();
  }

  public void createActivity(
      VisitDto visitDto, Locale locale, ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    Accounts account = accountsRepository.findByAccountId(visitDto.getCustomerId().toString());

    if (account == null) {
      throw new NotFoundDEException("AccountId Not found");
    }
    String sfdcAccountId = account.getAccountDocument().getGoldenRecordId();
    UUID externalRelatedListId = account.getAccountDocument().getId();
    List<UUID> externalNameListId = new ArrayList<>();
    List<String> sfdcContactId = new ArrayList<>();
    if (account.getAccountDocument().getContacts() != null) {
      for (Contact contact : account.getAccountDocument().getContacts()) {
        externalNameListId.add(contact.getContactId());
        sfdcContactId.add(contact.getSFDCContactId());
      }
    }
    Sites site = sitesRepository.findBySiteId(visitDto.getSiteId().toString());
    if (site == null) {
      throw new NotFoundDEException("SiteId Not found");
    }

    ActivityDocument activityDocument =
        ActivityDocument.builder()
            .id(UUID.randomUUID())
            .visitId(visitDto.getId())
            .sfdcAccountId(sfdcAccountId)
            .name(visitDto.getVisitName())
            .subject(
                visitDto.getVisitDate() != null
                    ? "Site Visit - " + visitDto.getVisitDate() + " - " + visitDto.getVisitName()
                    : "Site Visit - " + Instant.now() + " - " + visitDto.getVisitName())
            .needsSync(true)
            .nameListId(CollectionUtils.isNotEmpty(sfdcContactId) ? sfdcContactId.get(0) : null)
            .relatedToTypeID(RelatedToType.Account.ordinal())
            .nameTypeID(NameType.Contact.ordinal())
            .relatedToListId(sfdcAccountId)
            .externalNameListId(
                CollectionUtils.isNotEmpty(externalNameListId) ? externalNameListId.get(0) : null)
            .externalRelatedToListId(externalRelatedListId)
            .accountId(visitDto.getCustomerId())
            .eventStartDate(
                visitDto.getVisitDate() != null ? visitDto.getVisitDate() : Instant.now())
            .endDateTime(
                visitDto.getMobileLastUpdatedTime() != null
                    ? visitDto.getMobileLastUpdatedTime()
                    : Instant.now())
            .activityType(ActivityType.Event.getValue())
            .lastModifiedTimeUtc(Instant.now())
            .lastSyncTimeUtc(Instant.now())
            .activityDateTime(
                visitDto.getVisitDate() != null ? visitDto.getVisitDate() : Instant.now())
            .createTimeUtc(
                visitDto.getCreatedDate() != null ? visitDto.getCreatedDate() : Instant.now())
            .createUser(
                visitDto.getCreateUser() != null
                    ? visitDto.getCreateUser()
                    : userServiceImpl.getCurrentLoggedInUser())
            .lastModifyUser(
                visitDto.getLastModifyUser() != null
                    ? visitDto.getLastModifyUser()
                    : userServiceImpl.getCurrentLoggedInUser())
            .isNew(true)
            .assignedToID(
                (visitDto.getCreateUser() != null
                    ? visitDto.getCreateUser()
                    : userServiceImpl.getCurrentLoggedInUser()))
            .dataSource(account.getAccountDocument().getDataSource())
            .build();

    Activities activity = Activities.builder().activityDocument(activityDocument).build();
    activity = activitiesRepository.save(activity);

    if (account.getAccountDocument().getDataSource().equals(DataSource.LIFT)) {
      saveToLift(activity, account, site, visitDto, locale, resourceBundleMessageSource);
    } else {
      saveToCrescendo(account, site, visitDto, activity, locale, resourceBundleMessageSource);
    }
  }

  private void saveToCrescendo(
      Accounts account,
      Sites site,
      VisitDto visitDto,
      Activities activity,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource)
      throws CustomDEExceptions {

    log.info("Searching for User in Crescendo {}", userServiceImpl.getCurrentLoggedInUser());
    SalesforceRecordsResponse<Users> user =
        crescendoUserService.getByUserEmail(userServiceImpl.getCurrentLoggedInUser());

    EventsCrescendo crescendoEvent =
        mapActivityToCrescendoEvent(activity, account, site, user.getRecords().get(0), visitDto);

    Instant startDate =
        crescendoEvent.getStartDateTime() != null
            ? Instant.parse(crescendoEvent.getStartDateTime())
            : null;
    Instant endDate =
        crescendoEvent.getEndDateTime() != null
            ? Instant.parse(crescendoEvent.getEndDateTime())
            : null;

    if (account.getAccountDocument().getGoldenRecordId() != null
        && validateDate(startDate, endDate)) {
      String eventId =
          crescendoActivityService.createActivity(
              crescendoEvent, locale, resourceBundleMessageSource);
      activity.getActivityDocument().setEventSfdcId(eventId);
      activity.getActivityDocument().setReportLink(crescendoEvent.getReportLink());
      activity.getActivityDocument().setOwnerId(crescendoEvent.getOwnerId());

      activitiesRepository.save(activity);
    }
  }

  private EventsCrescendo mapActivityToCrescendoEvent(
      Activities activity, Accounts account, Sites site, Users user, VisitDto visitDto) {
    EventsCrescendo crescendoEvent = new EventsCrescendo();
    crescendoEvent.setEndDateTime(
        activity.getActivityDocument().getEndDateTime() != null
            ? activity.getActivityDocument().getEndDateTime().toString()
            : null);
    crescendoEvent.setExternalAccountId(account.getAccountDocument().getId().toString());
    crescendoEvent.setExternalId(site.getSiteDocument().getId().toString());
    crescendoEvent.setMobileFirst(true);
    crescendoEvent.setOwnerId(user.getId());
    crescendoEvent.setReportLink(getReportLink(visitDto));
    crescendoEvent.setStartDateTime(
        activity.getActivityDocument().getActivityDateTime() != null
            ? activity.getActivityDocument().getActivityDateTime().toString()
            : null);
    crescendoEvent.setSubject(activity.getActivityDocument().getSubject());
    crescendoEvent.setWhatId(activity.getActivityDocument().getSfdcAccountId());

    return crescendoEvent;
  }

  private void saveToLift(
      Activities activity,
      Accounts account,
      Sites site,
      VisitDto visitDto,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {

    ActivityDocument activityDocument = activity.getActivityDocument();
    EventDocument eventDocument =
        EventDocument.builder()
            .activityDateTime(
                activityDocument.getActivityDateTime() != null
                    ? activityDocument.getActivityDateTime().toString()
                    : null)
            .businessC(null)
            .deActivityExternalIdC(
                activityDocument.getId() != null
                    ? activityDocument.getId().toString()
                    : UUID.randomUUID().toString())
            .deSiteVisitIdC(
                visitDto.getId() != null
                    ? visitDto.getId().toString()
                    : UUID.randomUUID().toString())
            .endDateTime(
                activityDocument.getEndDateTime() != null
                    ? activityDocument.getEndDateTime().toString()
                    : null)
            .isAllDayEvent(false)
            .isReminderSet(true)
            .whatId(
                activityDocument.getSfdcAccountId() != null
                    ? activityDocument.getSfdcAccountId()
                    : null)
            .deSiteId(
                site.getSiteDocument().getExternalId() != null
                    ? site.getSiteDocument().getExternalId()
                    : null)
            .startDateTime(
                activityDocument.getActivityDateTime() != null
                    ? activityDocument.getActivityDateTime().toString()
                    : null)
            .subject(activityDocument.getSubject())
            .typeC("Dairy Site Visit")
            .build();

    // get report if it is generated before sync
    eventDocument.setReportLinkC(getReportLink(visitDto));
    log.debug("Checking for user: {}", userServiceImpl.getCurrentLoggedInUser());
    User user = liftUserService.findOwner(userServiceImpl.getCurrentLoggedInUser());
    Instant startDate =
        eventDocument.getStartDateTime() != null
            ? Instant.parse(eventDocument.getStartDateTime())
            : null;
    Instant endDate =
        eventDocument.getEndDateTime() != null
            ? Instant.parse(eventDocument.getEndDateTime())
            : null;
    if (validateDate(startDate, endDate)) {
      if (DataSource.LIFT.equals(account.getAccountDocument().getDataSource())) {
        if (account.getAccountDocument().getGoldenRecordId() != null
            && site.getSiteDocument().getExternalId() != null
            && user != null) {
          log.debug("LIFT Account,Site and User found for event creation");
          eventDocument.setOwnerId(user.getId());

          saveToSf(eventDocument, locale, resourceBundleMessageSource);
        } else {
          activity.setDeleted(true);
          activitiesRepository.save(activity);
          liftUtils.siteValidationError(user, account, site, locale, resourceBundleMessageSource);
        }
      } else {
        activity.getActivityDocument().setOwnerId(userServiceImpl.getCurrentLoggedInUser());
        activity.getActivityDocument().setReportLink(eventDocument.getReportLinkC());
        activitiesRepository.save(activity);
      }
    }
  }

  private boolean validateDate(Instant startDate, Instant endDate) {
    if (startDate != null && endDate != null) {
      Duration duration = Duration.between(startDate, endDate);

      // Check if the duration is greater than seconds in 14 days, using seconds as comparator
      // because There are cases where this statement will become true for 14 days i.e 14 days and 3
      // seconds. Hence checking on basis of seconds. (Events cannot be created for visits older
      // than 14 days)
      if (duration.toSeconds() > (14 * 24 * 60 * 60)) {
        return false; // If greater than 14 days, return false
      }
    }
    return true;
  }

  public String getReportLink(VisitDto visitDto) {
    Visits visit = visitsRepository.findByVisitId(visitDto.getId().toString());
    if (!Objects.isNull(visit)
        && !Objects.isNull(visit.getVisitDocument().getReportType())
        && !visit.getVisitDocument().getReportType().isEmpty()) {
      List<ReportType> reportType =
          visit.getVisitDocument().getReportType().stream()
              .filter(report -> report.getUrl() != null)
              .toList();
      if (!reportType.isEmpty()) {
        return reportType.get(0).getUrl();
      }
    }
    return null;
  }

  public void saveToSf(
      EventDocument eventDocument,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource)
      throws IllegalAccessException, ClassNotFoundException, JsonProcessingException,
          CustomDEExceptions {

    Activities activity =
        activitiesRepository.findByDocumentIdAndDeleted(eventDocument.getDeActivityExternalIdC());
    EventUpdateModel eventModel = liftEventServiceImpl.documentToModel(eventDocument);
    log.debug("VALIDATING EVENTS MODEL");
    PayloadValidationDto eventValidation =
        liftApiService.validate(
            eventModel, LiftEntityName.EVENT, locale, resourceBundleMessageSource);
    if (!eventValidation.getErrorDetails().isEmpty()) {
      activity.setDeleted(true);
      activitiesRepository.save(activity);
      eventValidation.getErrorDetails().stream().forEach(validation -> validation.setEntity(EVENT));
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(eventValidation.getErrorDetails()));
    }
    log.debug("EVENTS VALIDATED");

    liftEventServiceImpl.createEvent(eventDocument, locale, resourceBundleMessageSource);
    activity.getActivityDocument().setEventSfdcId(eventDocument.getEventId());
    activity.getActivityDocument().setReportLink(eventDocument.getReportLinkC());
    activity.getActivityDocument().setOwnerId(eventDocument.getOwnerId());
    activitiesRepository.save(activity);
  }

  @Override
  public VisitDto update(
      VisitDto visitDto, Locale locale, ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    Visits visit = visitsRepository.findByVisitId(visitDto.getId().toString());
    if (visit == null) {
      throw new NotFoundDEException("No visit found against ID: " + visitDto.getId());
    }

    if ((Objects.equals(visit.getVisitDocument().getStatus(), VisitStatus.InProgress))
        && (Objects.equals(visitDto.getStatus(), VisitStatus.Published))
        && !(activitiesRepository.existByVisitId(visitDto.getId().toString()))) {
      createActivity(visitDto, locale, resourceBundleMessageSource);
    }
    Visits visitToPersist = mapToModelForUpdate(visitDto, visit);
    visitToPersist.setId(visit.getId());
    visitToPersist.setCreatedDate(visit.getCreatedDate());
    updateSiteVisitList(visitToPersist);
    visitToPersist.getVisitDocument().setLastModifiedTimeUtc(Instant.now());
    visitToPersist = visitsRepository.save(visitToPersist);
    return mapToDto(visitToPersist);
  }

  // This function checks if the tool inside visit is newly created on basis of ID. If ID exists,
  // the tool needs to be updated, otherwise it is created.
  private Visits mapToModelForUpdate(VisitDto visitDto, Visits visit) {
    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUser();
    setVisitAttributesForUpdate(visit, visitDto);
    if (validateTool(
        visitDto.getRumenHealth(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getRumenHealth()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getRumenHealth())))) {
      visit
          .getVisitDocument()
          .setRumenHealth(visitMapper.dtoToModelForRumenHealth(visitDto.getRumenHealth()));
      visitMapper.updateDefaultAttributesForRumenHealthCudChewing(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getRumenHealth().getId()));
    }
    if (validateTool(
        visitDto.getMetabolicIncidence(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getMetabolicIncidence()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getMetabolicIncidence())))) {
      visit
          .getVisitDocument()
          .setMetabolicIncidence(
              visitMapper.dtoToModelForMetabolicIncidence(visitDto.getMetabolicIncidence()));
      visitMapper.updateDefaultAttributesForMetabolicIncidence(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getMetabolicIncidence().getId()));
    }
    if (validateTool(
        visitDto.getBodyCondition(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getBodyCondition()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getBodyCondition())))) {
      visit
          .getVisitDocument()
          .setBodyCondition(visitMapper.dtoToModelForBCS(visitDto.getBodyCondition()));
      visitMapper.updateDefaultAttributesForBCS(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getBodyCondition().getId()));
    }
    if (validateTool(
        visitDto.getLocomotionScore(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getLocomotionScore()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getLocomotionScore())))) {
      visit
          .getVisitDocument()
          .setLocomotionScore(visitMapper.dtoToModelForLocomotion(visitDto.getLocomotionScore()));
      visitMapper.updateDefaultAttributesForLocomotionScore(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getLocomotionScore().getId()));
    }

    if (validateTool(
        visitDto.getAnimalAnalysis(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getAnimalAnalysis()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getAnimalAnalysis())))) {
      visit
          .getVisitDocument()
          .setAnimalAnalysis(visitMapper.dtoToModelForAnimalAnalysis(visitDto.getAnimalAnalysis()));
      visitMapper.updateDefaultAttributesForAnimalAnalysis(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getAnimalAnalysis().getId()));
    }
    if (validateTool(
        visitDto.getMilkSoldEvaluation(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getMilkSoldEvaluation()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getMilkSoldEvaluation())))) {
      visit
          .getVisitDocument()
          .setMilkSoldEvaluation(
              visitMapper.dtoToModelForMilkSoldEvaluation(visitDto.getMilkSoldEvaluation()));
      visitMapper.updateDefaultAttributesForMilkSoldEvaluation(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getMilkSoldEvaluation().getId()));
    }
    if (validateTool(
        visitDto.getRoboticMilkEvaluation(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getRoboticMilkEvaluation()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getRoboticMilkEvaluation())))) {
      visit
          .getVisitDocument()
          .setRoboticMilkEvaluation(
              visitMapper.dtoToModelForRoboticMilkEvaluation(visitDto.getRoboticMilkEvaluation()));
      visitMapper.updateDefaultAttributesForRoboticMilkEvaluation(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getRoboticMilkEvaluation().getId()));
    }

    if (validateTool(
        visitDto.getRumenHealthManureScore(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getRumenHealthManureScore()),
        getMobileLastUpdatedDate(
            visitMapper.toolToHashMap(visitDto.getRumenHealthManureScore())))) {
      visit
          .getVisitDocument()
          .setRumenHealthManureScore(
              visitMapper.dtoToModelForRumenHealthManureScore(
                  visitDto.getRumenHealthManureScore()));
      visitMapper.updateDefaultAttributesForRumenHealthManureScore(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getRumenHealthManureScore().getId()));
    }
    if (validateTool(
        visitDto.getPileAndBunker(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getPileAndBunker()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getPileAndBunker())))) {
      visit
          .getVisitDocument()
          .setPileAndBunker(visitMapper.dtoToModelForPileAndBunker(visitDto.getPileAndBunker()));
      visitMapper.updateDefaultAttributesForPileAndBunker(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getPileAndBunker().getId()));
    }
    if (validateTool(
        visitDto.getRumenFillManureScore(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getRumenFillManureScore()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getRumenFillManureScore())))) {
      visit
          .getVisitDocument()
          .setRumenFillManureScore(
              visitMapper.dtoToModelForRumenFillManureScore(visitDto.getRumenFillManureScore()));
      visitMapper.updateDefaultAttributesForRumenFillManureScore(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getRumenFillManureScore().getId()));
    }
    if (validateTool(
        visitDto.getTmrParticleScore(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getTmrParticleScore()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getTmrParticleScore())))) {
      visit
          .getVisitDocument()
          .setTmrParticleScore(
              visitMapper.dtoToModelForTmrParticleScore(visitDto.getTmrParticleScore()));
      visitMapper.updateDefaultAttributesForTmrParticleScore(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getTmrParticleScore().getId()));
    }
    if (validateTool(
        visitDto.getForagePennState(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getForagePennState()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getForagePennState())))) {
      visit
          .getVisitDocument()
          .setForagePennState(
              visitMapper.dtoToModelForForagePennState(visitDto.getForagePennState()));
      visitMapper.updateDefaultAttributesForForagePennState(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getForagePennState().getId()));
    }

    if (validateTool(
        visitDto.getHeatStress(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getHeatStress()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getHeatStress())))) {
      visit
          .getVisitDocument()
          .setHeatStress(visitMapper.dtoToModelForHeatStress(visitDto.getHeatStress()));
      visitMapper.updateDefaultAttributesForHeatStress(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getHeatStress().getId()));
    }

    if (validateTool(
        visitDto.getPenTimeBudgetTool(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getPenTimeBudgetTool()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getPenTimeBudgetTool())))) {
      visit
          .getVisitDocument()
          .setPenTimeBudgetTool(
              visitMapper.dtoToModelForPenTimeBudget(visitDto.getPenTimeBudgetTool()));
      visitMapper.updateDefaultAttributesForPenTimeBudget(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getPenTimeBudgetTool().getId()));
    }
    if (validateTool(
        visitDto.getManureScreenerTool(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getManureScreenerTool()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getManureScreenerTool())))) {
      visit
          .getVisitDocument()
          .setManureScreenerTool(
              visitMapper.dtoToModelForManureScreenerTool(visitDto.getManureScreenerTool()));
      visitMapper.updateDefaultAttributesForManureScreenerTool(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getManureScreenerTool().getId()));
    }
    if (validateTool(
        visitDto.getForageAuditScorecard(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getForageAuditScorecard()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getForageAuditScorecard())))) {
      visit
          .getVisitDocument()
          .setForageAuditScorecard(
              visitMapper.dtoToModelForForageAuditScorecard(visitDto.getForageAuditScorecard()));
      visitMapper.updateDefaultAttributesForForageAuditScorecard(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getForageAuditScorecard().getId()));
    }
    if (validateTool(
        visitDto.getProfitabilityAnalysisTool(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getProfitabilityAnalysis()),
        getMobileLastUpdatedDate(
            visitMapper.toolToHashMap(visitDto.getProfitabilityAnalysisTool())))) {
      visit
          .getVisitDocument()
          .setProfitabilityAnalysis(
              visitMapper.dtoToModelForProfitabilityAnalysis(
                  visitDto.getProfitabilityAnalysisTool()));
      visitMapper.updateDefaultAttributesForProfatibilityAnalysis(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getProfitabilityAnalysisTool().getId()));
    }
    if (validateTool(
        visitDto.getCalfHeiferScorecard(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getCalfHeiferScorecard()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getCalfHeiferScorecard())))) {
      visit
          .getVisitDocument()
          .setCalfHeiferScorecard(
              visitMapper.dtoToModelForCalfHeiferScorecard(visitDto.getCalfHeiferScorecard()));
      visitMapper.updateDefaultAttributesForCalfHeiferScorecard(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getCalfHeiferScorecard().getId()));
    }

    if (validateTool(
        visitDto.getReturnOverFeedTool(),
        visitMapper.toolToHashMap(visit.getVisitDocument().getReturnOverFeedTool()),
        getMobileLastUpdatedDate(visitMapper.toolToHashMap(visitDto.getReturnOverFeedTool())))) {
      visit
          .getVisitDocument()
          .setReturnOverFeedTool(
              visitMapper.dtoToModelForReturnOverFeed(visitDto.getReturnOverFeedTool()));
      visitMapper.updateDefaultAttributesForReturnOverFeed(
          visit,
          currentLoggedInUser,
          currentLoggedInUser,
          isToolUpdate(visitDto.getReturnOverFeedTool().getId()));
    }

    return visit;
  }

  // This method checks if the tool is correct for insertion

  private Instant getMobileLastUpdatedDate(Map<String, Object> toolMap) {
    if (toolMap.isEmpty()) {
      return null;
    }
    return (toolMap.get("mobileLastUpdatedTime") != null
        ? Instant.parse(toolMap.get("mobileLastUpdatedTime").toString())
        : null);
  }

  /* 3 cases are handled in this function:
   * 1- If Tool provided in DTO from mobile side is null, then there is no need to update the tool.
   * 2- If Tool saved is database is null (First time creation), then return true and let it create the new tool in the visit.
   * 3- If Tool last updated time in database > tool last mobile updated time then return true.
   */
  private boolean validateTool(
      Object toolInDto, Map<String, Object> toolModelMap, Instant mobileLastUpdatedTime) {
    if (toolInDto == null) {
      return false;
    }
    if (toolModelMap.isEmpty() || toolModelMap.get("MobileLastUpdatedTime") == null) {
      return true;
    } else {
      Instant lastUpdatedTime = Instant.parse(toolModelMap.get("MobileLastUpdatedTime").toString());
      if (mobileLastUpdatedTime.compareTo(lastUpdatedTime) > 0) {
        return true;
      }
    }
    return false;
  }

  // This method returns whether the tool requires update or creation
  private boolean isToolUpdate(UUID id) {
    return (id != null);
  }
  // This method updates the visit's delta
  private void setVisitAttributesForUpdate(Visits visit, VisitDto visitDto) {
    if (visitDto.getIsVisitAutoPublished() != null) {
      visit.getVisitDocument().setIsVisitAutoPublished(visitDto.getIsVisitAutoPublished());
    }
    if (visitDto.getNeedsSync() != null) {
      visit.getVisitDocument().setNeedsSync(visitDto.getNeedsSync());
    }
    if (visitDto.getSelected() != null) {
      visit.getVisitDocument().setSelected(visitDto.getSelected());
    }
    if (visitDto.getFirstName() != null) {
      visit.getVisitDocument().setFirstName(visitDto.getFirstName());
    }
    if (visitDto.getFormattedCreationDate() != null) {
      visit.getVisitDocument().setFormattedCreationDate(visitDto.getFormattedCreationDate());
    }
    if (visitDto.getLastName() != null) {
      visit.getVisitDocument().setLastName(visitDto.getLastName());
    }
    if (visitDto.getSelectedCurrency() != null) {
      visit.getVisitDocument().setSelectedCurrency(visitDto.getSelectedCurrency());
    }
    if (visitDto.getStatus() != null) {
      visit.getVisitDocument().setStatus(visitDto.getStatus());
    }
    if (visitDto.getVisitDate() != null) {
      visit.getVisitDocument().setVisitDate(visitDto.getVisitDate());
    }
    if (visitDto.getVisitName() != null) {
      visit.getVisitDocument().setVisitName(visitDto.getVisitName());
    }
    if (visitDto.getVisitPublishedDateTimeUtc() != null) {
      visit
          .getVisitDocument()
          .setVisitPublishedDateTimeUtc(visitDto.getVisitPublishedDateTimeUtc());
    }
    if (visitDto.getUnitOfMeasure() != null) {
      visit.getVisitDocument().setUnitOfMeasure(visitDto.getUnitOfMeasure());
    }
    if (visitDto.getSelectedPointScale() != null) {
      visit.getVisitDocument().setSelectedPointScale(visitDto.getSelectedPointScale());
    }

    visit.setDeleted(visitDto.isDeleted());
    visit.getVisitDocument().setMobileLastUpdatedTime(visitDto.getMobileLastUpdatedTime());
    visit.getVisitDocument().setPensUsed(visitDto.getUsedPens());
  }

  private void updateSiteVisitList(Visits visitToPersist) throws NotFoundDEException {
    Sites site =
        sitesRepository.findBySiteId(visitToPersist.getVisitDocument().getSiteId().toString());

    if (site == null) {
      throw new NotFoundDEException(
          "No site found against Site ID:" + visitToPersist.getVisitDocument().getSiteId());
    }

    for (SiteVisit siteVisit : site.getSiteDocument().getVisits()) {
      if (siteVisit
          .getLabyrinthVisitId()
          .toString()
          .equalsIgnoreCase(visitToPersist.getVisitDocument().getId().toString())) {
        siteVisit.setStatus(visitToPersist.getVisitDocument().getStatus());
        siteVisit.setVisitDate(visitToPersist.getVisitDocument().getVisitDate());
        siteVisit.setVisitName(visitToPersist.getVisitDocument().getVisitName());
      }
    }

    sitesRepository.save(site);
  }

  @Override
  public PageImpl<VisitDto> getAllVisitsBySearchPaginated(
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      VisitSearchDto visitSearchDto) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    FilterSpecification spec = setSpecifications(visitSearchDto);
    Page<Visits> visits = visitsRepository.findAll(spec, pageable);
    if (visits.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        visits.stream().map(this::mapToDto).toList(), pageable, visits.getTotalElements());
  }

  private FilterSpecification setSpecifications(VisitSearchDto visitSearchDto) {
    FilterSpecification spec = new FilterSpecification();

    spec.add(addCustomerIds(visitSearchDto));

    spec.add(addSiteIds(visitSearchDto));
    if (!Objects.isNull(visitSearchDto.getVisitStatus())) {
      spec.add(
          new SearchCriteria(
              SearchKey.VISIT_STATUS, SearchOperation.IN, visitSearchDto.getVisitStatus()));
    }

    if (!Objects.isNull(visitSearchDto.getTo()) && !Objects.isNull(visitSearchDto.getFrom())) {
      spec.add(
          new SearchCriteria(
              SearchKey.VISIT_DATE,
              SearchOperation.BETWEEN,
              visitSearchDto.getFrom().toString() + "," + visitSearchDto.getTo().toString()));
    }
    if (!Objects.isNull(visitSearchDto.getTools())) {
      visitSearchDto.getTools().stream()
          .forEach(
              tool -> {
                spec.add(new SearchCriteria(tool, SearchOperation.NOT_NULL, null));
              });
    }

    return spec;
  }

  private SearchCriteria addSiteIds(VisitSearchDto visitSearchDto) {
    if (!Objects.isNull(visitSearchDto.getSiteId())) {
      setSiteNames(visitSearchDto.getSiteId());
      return new SearchCriteria(SearchKey.SITE_ID, SearchOperation.IN, visitSearchDto.getSiteId());
    } else {
      String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
      List<String> accountIdsByUser =
          accountsRepository.findAccountIdsByUserWithAllFlags(
              currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
      List<Sites> sites = sitesRepository.findSitesByAccountIds(accountIdsByUser);
      ArrayList<String> siteIds = new ArrayList<>();
      if (Objects.isNull(sites) || sites.isEmpty()) {
        return new SearchCriteria(SearchKey.SITE_ID, SearchOperation.IN, "");
      } else {
        for (Sites site : sites) {
          if (!siteNames.containsKey(site.getSiteDocument().getId().toString())) {
            siteNames.put(
                site.getSiteDocument().getId().toString(), site.getSiteDocument().getSiteName());
          }
          siteIds.add(site.getSiteDocument().getId().toString());
        }
        return new SearchCriteria(SearchKey.SITE_ID, SearchOperation.IN, siteIds);
      }
    }
  }

  private void setSiteNames(List<String> siteIds) {
    List<Sites> sites = sitesRepository.findBySiteIds(siteIds);
    sites.stream()
        .forEach(
            site -> {
              if (!siteNames.containsKey(site.getSiteDocument().getId().toString())) {
                siteNames.put(
                    site.getSiteDocument().getId().toString(),
                    site.getSiteDocument().getSiteName());
              }
            });
  }

  private SearchCriteria addCustomerIds(VisitSearchDto visitSearchDto) {
    if (!Objects.isNull(visitSearchDto.getCustomerId())) {
      setAccountNames(visitSearchDto.getCustomerId());
      return new SearchCriteria(
          SearchKey.CUSTOMER_ID, SearchOperation.IN, visitSearchDto.getCustomerId());
    } else {
      String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
      List<Accounts> accounts =
          accountsRepository.findAccountsByUserWithAllFlags(
              currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
      ArrayList<String> accountIds = new ArrayList<>();
      if (Objects.isNull(accounts) || accounts.isEmpty()) {
        return new SearchCriteria(SearchKey.CUSTOMER_ID, SearchOperation.IN, "");
      } else {
        for (Accounts account : accounts) {
          if (!accountNames.containsKey(account.getAccountDocument().getId().toString())) {
            accountNames.put(
                account.getAccountDocument().getId().toString(),
                account.getAccountDocument().getAccountName());
          }
          accountIds.add(account.getAccountDocument().getId().toString());
        }
        return new SearchCriteria(SearchKey.CUSTOMER_ID, SearchOperation.IN, accountIds);
      }
    }
  }

  private void setAccountNames(List<String> customerId) {
    List<Accounts> accounts = accountsRepository.findAccountsByAccountIds(customerId);
    accounts.stream()
        .forEach(
            account -> {
              if (!accountNames.containsKey(account.getAccountDocument().getId().toString())) {
                accountNames.put(
                    account.getAccountDocument().getId().toString(),
                    account.getAccountDocument().getAccountName());
              }
            });
  }

  @Override
  public VisitPublishResponseDto autoPublish(
      List<VisitPublishDto> visitPublishDto,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    List<UUID> notPublished = new ArrayList<>();
    List<UUID> published = new ArrayList<>();
    for (VisitPublishDto visitToPublish : visitPublishDto) {
      Visits visit = visitsRepository.findByVisitId(visitToPublish.getVisitId().toString());
      if (Objects.isNull(visit)) {
        notPublished.add(visitToPublish.getVisitId());
      } else {
        if (!activitiesRepository.existByVisitId(visitToPublish.getVisitId().toString())) {
          VisitDto visitDto = mapToDto(visit);
          createActivity(visitDto, locale, resourceBundleMessageSource);
        }
        updateVisitAttributes(visit);
        Sites site = updateVisitsInSite(visit);
        Accounts account = updateAccountInfo(visit);
        sitesRepository.save(site);
        accountsRepository.save(account);
        published.add(visitToPublish.getVisitId());
        visitsRepository.save(visit);
      }
    }
    return VisitPublishResponseDto.builder()
        .publishedVisits(published)
        .notPublishedVisits(notPublished)
        .build();
  }

  @Override
  public Page<ISelectDto> getVisitIdAndNameList(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIds = accountsRepository.findAccountIdsByUser(currentLoggedUser);

    Page<ISelectDto> visits = visitsRepository.findByAccountIds(accountIds, lastSyncTime, pageable);
    if (Objects.isNull(visits)) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(visits.stream().toList(), pageable, visits.getTotalElements());
  }

  private Accounts updateAccountInfo(Visits visit) {
    Accounts account =
        accountsRepository.findByAccountId(visit.getVisitDocument().getCustomerId().toString());
    account.getAccountDocument().setLastSyncTimeUtc(Instant.now());
    account.getAccountDocument().setNeedsSync(true);
    account.getAccountDocument().setLastModifiedTimeUtc(Instant.now());
    return account;
  }

  private Sites updateVisitsInSite(Visits visit) {
    Sites site = sitesRepository.findBySiteId(visit.getVisitDocument().getSiteId().toString());

    for (SiteVisit visitsInSite : site.getSiteDocument().getVisits()) {
      if (visit.getVisitDocument().getId().equals(visitsInSite.getLabyrinthVisitId())) {
        visitsInSite.setStatus(VisitStatus.Published);
      }
    }
    return site;
  }

  private Visits updateVisitAttributes(Visits visit) {
    visit.getVisitDocument().setStatus(VisitStatus.Published);
    visit.getVisitDocument().setLastSyncTimeUtc(Instant.now());
    visit.getVisitDocument().setNeedsSync(true);
    visit.getVisitDocument().setVisitPublishedDateTimeUtc(Instant.now());
    visit.getVisitDocument().setLastModifiedTimeUtc(Instant.now());
    return visit;
  }
}
