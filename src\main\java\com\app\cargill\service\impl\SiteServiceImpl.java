/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static com.app.cargill.constants.ApplicationMapping.LM_SITE_SYSTEM_NAME;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoApplicationMappingService;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.document.Barn;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.SiteVisit;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.dto.SiteDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.ISiteMappingService;
import com.app.cargill.service.ISiteService;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.impl.mappers.SiteMapper;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.constants.LiftEntityName;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.ExternalDataSourceUpdateModel;
import com.app.cargill.sf.cc.model.simple.SiteUpdateModel;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.app.cargill.sf.cc.utils.LiftUtils;
import com.app.cargill.utils.JsonUtils;
import com.app.cargill.utils.PageableUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service("SiteServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings("java:S125") // @TODO remove this once commented out code is removed
public class SiteServiceImpl implements ISiteService {

  private static final String ITEM_NOT_FOUND = "%1$s with id %2$s not found";
  private final SitesRepository sitesRepository;
  private final AccountsRepository accountsRepository;
  private final ISiteMappingService siteMappingServiceImpl;
  private final IUserService userServiceImpl;
  private final PensRepository pensRepository;
  private final LiftSitesService liftSitesServiceImpl;
  private final LiftUtils liftUtils;
  private final LiftApiService liftApiService;
  private final LiftSiteMappingsService liftSiteMappingsService;
  private final LiftUserService liftUserService;
  @Setter public Long waitTimeForAccount = 2000L;
  private static final String SITE = "Site";
  private static final String SITEMAPPING = "SiteMapping";
  private final ICrescendoApplicationMappingService applicationMappingService;
  private final ICrescendoUserService crescendoUserService;
  private final LiftConfig liftConfig;

  @Value("${app.configuration.auto-publish-visit-days:5}")
  private int autoPublishVisitDays;

  Map<String, Long> getPenCountBySiteId(Page<Sites> sites) {
    List<String> siteIds =
        sites.stream()
            .parallel()
            .filter(s -> !Objects.isNull(s.getSiteDocument().getId()))
            .map(s -> s.getSiteDocument().getId().toString())
            .toList();
    List<Pens> penCounts = pensRepository.findBySiteIds(siteIds);
    if (penCounts != null && !penCounts.isEmpty()) {
      return penCounts.stream()
          .filter(p -> p.getPenDocument().getSiteId() != null)
          .collect(
              Collectors.groupingBy(
                  p -> p.getPenDocument().getSiteId().toString(), Collectors.counting()));
    }
    return new HashMap<>();
  }

  @Override
  public Page<SiteDto> getAllSitesByCurrentLoggedInUser(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
    Page<Sites> sites =
        sitesRepository.findByUpdatedDateAndAccountsUsers(accountIdsByUser, lastSyncTime, pageable);
    if (Objects.isNull(sites)) {
      return new PageImpl<>(new ArrayList<>());
    }

    Map<String, Long> siteToPenCount = getPenCountBySiteId(sites);
    return new PageImpl<>(
        sites.stream().map(site -> modelToDto(site, siteToPenCount)).toList(),
        pageable,
        sites.getTotalElements());
  }

  @Override
  public List<String> getFilteredSiteIds() {
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
    List<String> siteIds = sitesRepository.findSiteIdsByAccountIds(accountIdsByUser);
    if (Objects.isNull(siteIds)) {
      return new ArrayList<>();
    }

    return siteIds;
  }

  @Override
  public Page<SiteDto> getAllSitesByAccountId(
      String accountId, int page, int size, String sortBy, Instant lastSyncTime, String sorting) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);

    Page<Sites> sites =
        sitesRepository.findByAccountIdAndUpdatedDate(accountId, lastSyncTime, pageable);
    if (Objects.isNull(sites)) {
      return new PageImpl<>(new ArrayList<>());
    }
    Map<String, Long> siteToPenCount = getPenCountBySiteId(sites);
    return new PageImpl<>(
        sites.stream().map(site -> modelToDto(site, siteToPenCount)).toList(),
        pageable,
        sites.getTotalElements());
  }

  void updateSiteMappings(Sites site) {
    if (site.getSiteDocument().getOrigination().contentEquals(LM_SITE_SYSTEM_NAME)) {
      if (CollectionUtils.isEmpty(site.getSiteDocument().getDataSourceMappings())) {
        site.getSiteDocument()
            .setDataSourceMappings(
                Arrays.asList(
                    DataSourceMapping.builder()
                        .systemId(site.getSiteDocument().getId().toString())
                        .systemName(LM_SITE_SYSTEM_NAME)
                        .build()));
      } else {
        boolean lmSiteExits =
            site.getSiteDocument().getDataSourceMappings().stream()
                    .filter(s -> s.getSystemName().contentEquals(LM_SITE_SYSTEM_NAME))
                    .count()
                > 0;
        if (!lmSiteExits) {
          site.getSiteDocument()
              .getDataSourceMappings()
              .add(
                  DataSourceMapping.builder()
                      .systemId(site.getSiteDocument().getId().toString())
                      .systemName(LM_SITE_SYSTEM_NAME)
                      .build());
        }
      }
    }
  }

  @Override
  public void updateSiteMappingsMax(Sites site, SiteMappingDocument mapping) {
    log.trace("[SiteServiceImpl][updateSiteMappingsMax] Entering ...");

    updateMapping(site, LM_SITE_SYSTEM_NAME, site.getSiteDocument().getId().toString());
    updateMapping(site, ApplicationMapping.DDW_SYSTEM_NAME, mapping.getDdwHerdId());
    updateMapping(
        site,
        ApplicationMapping.MAX_SYSTEM_NAME,
        mapping.getMaxSiteId() != null ? mapping.getMaxSiteId().toString() : null);
    //    updateMapping(site, ApplicationMapping.MP_SYSTEM_NAME, mapping.getMilkProcessorId());
    //    updateMapping(site, ApplicationMapping.DCGO_SYSTEM_NAME, mapping.getDcgoId());
  }

  private void updateMapping(Sites site, String systemName, String systemId) {
    log.trace("[SiteServiceImpl][updateMapping] Entering ...");
    log.debug(
        "[SiteServiceImpl][updateMapping] SiteId: "
            + site.getSiteDocument().getId()
            + ", systemName: "
            + systemName
            + ", systemId: "
            + systemId);

    List<DataSourceMapping> mappings =
        new ArrayList<>(site.getSiteDocument().getDataSourceMappings());
    DataSourceMapping dataSourceMapping =
        mappings.stream()
            .filter(m -> m.getSystemName().equalsIgnoreCase(systemName))
            .findFirst()
            .orElse(null);
    log.debug(
        "[SiteServiceImpl][updateMapping] mapping: "
            + (dataSourceMapping != null ? dataSourceMapping.getSystemId() : "null"));
    if (systemId == null || systemId.isBlank()) {
      if (dataSourceMapping != null) {
        mappings.removeIf(m -> m.getSystemName().equalsIgnoreCase(systemName));
        site.getSiteDocument().setDataSourceMappings(mappings);
        site.getSiteDocument().setLastSyncTimeUtc(Instant.now());
      }
    } else {
      if (dataSourceMapping == null) {
        dataSourceMapping =
            DataSourceMapping.builder().systemName(systemName).systemId(systemId).build();
        mappings.add(dataSourceMapping);
        site.getSiteDocument().setDataSourceMappings(mappings);
        site.getSiteDocument().setLastSyncTimeUtc(Instant.now());
      } else if (!dataSourceMapping.getSystemId().equals(systemId)) {
        dataSourceMapping.setSystemId(systemId);
        site.getSiteDocument().setLastSyncTimeUtc(Instant.now());
      }
    }
  }

  @Override
  public SiteDto save(SiteDto siteDto, Locale locale, ResourceBundleMessageSource source)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {

    if (Objects.isNull(siteDto.getLocalId())) {
      throw new IllegalArgumentException("LocalId is null");
    }
    List<Sites> sitesExits = sitesRepository.findByLocalId(siteDto.getLocalId());
    if (sitesExits != null && !sitesExits.isEmpty()) {
      Map<String, Long> siteToPenCount = getPenCountBySiteId(new PageImpl<>(sitesExits));
      return modelToDto(sitesExits.get(0), siteToPenCount);
    }

    Accounts byAccountId = accountsRepository.findByAccountId(siteDto.getAccountId().toString());
    if (byAccountId == null) {
      throw new NotFoundDEException(
          String.format(ITEM_NOT_FOUND, "Account", siteDto.getAccountId()));
    }

    // setting up model from dto
    Sites sites = dtoToModel(siteDto, byAccountId);
    updateSiteMappings(sites);

    // update foreign keys
    updateForeignKeys(sites);

    setSiteProperties(byAccountId.getAccountDocument().getId(), sites, true);
    // create a barn if not exists already
    if (CollectionUtils.isEmpty(sites.getSiteDocument().getBarns())) {
      sites
          .getSiteDocument()
          .setBarns(
              List.of(
                  Barn.builder()
                      .id(UUID.randomUUID())
                      .createUser(userServiceImpl.getCurrentLoggedInUser())
                      .build()));
    }
    if (CollectionUtils.isEmpty(sites.getSiteDocument().getVisits())) {
      sites.getSiteDocument().setVisits(new ArrayList<>());
    }

    Map<String, Long> siteToPenCount = getPenCountBySiteId(new PageImpl<>(List.of(sites)));

    if (sites.getSiteDocument().getId() != null
        && sites.getSiteDocument().getAccountId() != null
        && byAccountId.getAccountDocument().getDataSource().equals(DataSource.LIFT)) {
      saveToLift(sites, byAccountId, locale, source);
    } else {
      saveToCrescendo(sites, byAccountId, locale, source);
    }
    if (!Objects.isNull(sites.getSiteDocument().getDataSourceMappings())) {
      siteMappingServiceImpl.createLiftSiteMapping(sites);
    }
    sites = sitesRepository.saveAndFlush(sites);
    updateAccounts(sites.getSiteDocument().getVisits(), byAccountId);

    return modelToDto(sites, siteToPenCount);
  }

  private void saveToCrescendo(
      Sites sites, Accounts byAccountId, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions {

    if (byAccountId.getAccountDocument().getGoldenRecordId() == null) {
      throw new CustomDEExceptions("Account hasn't been synced to Crescendo");
    }

    if (sites.getSiteDocument().getExternalAccountId() == null) {
      sites
          .getSiteDocument()
          .setExternalAccountId(byAccountId.getAccountDocument().getGoldenRecordId());
    }

    SalesforceRecordsResponse<Users> user =
        crescendoUserService.getByUserEmail(userServiceImpl.getCurrentLoggedInUser());

    if (Objects.nonNull(user)) {

      SiteMappingDocument siteMappingDocument =
          SiteMappingDocument.builder()
              .labyrinthAccountId(byAccountId.getAccountDocument().getId())
              .labyrinthSiteId(sites.getSiteDocument().getId())
              .build();
      applicationMappingService.createApplicationMapping(
          siteMappingDocument,
          byAccountId.getAccountDocument().getGoldenRecordId(),
          locale,
          source);
    }
  }

  public void saveToLift(
      Sites site, Accounts salesforceAccount, Locale locale, ResourceBundleMessageSource source)
      throws IllegalAccessException, ClassNotFoundException, JsonProcessingException,
          CustomDEExceptions {

    log.debug("Checking for user: {}", userServiceImpl.getCurrentLoggedInUser());
    User user = liftUserService.findOwner(userServiceImpl.getCurrentLoggedInUser());
    if (salesforceAccount.getAccountDocument().getGoldenRecordId() != null && user != null) {
      site.getSiteDocument()
          .setExternalAccountId(salesforceAccount.getAccountDocument().getGoldenRecordId());

      SiteUpdateModel siteUpdateModel =
          liftSitesServiceImpl.documentToModel(site.getSiteDocument());
      log.debug("VALIDATING SITE");
      PayloadValidationDto siteValidation =
          liftApiService.validate(siteUpdateModel, LiftEntityName.SITE, locale, source);
      if (!siteValidation.getErrorDetails().isEmpty()) {
        siteValidation.getErrorDetails().stream().forEach(validation -> validation.setEntity(SITE));
        throw new CustomDEExceptions(
            JsonUtils.toJsonWithoutPrettyPrinter(siteValidation.getErrorDetails()));
      }
      log.debug("SITE VALIDATED");

      log.debug("VALIDATING SITE MAPPING DATA");
      ExternalDataSourceUpdateModel siteMappingUpdateModel =
          liftSiteMappingsService.documentToModel(site.getSiteDocument());
      PayloadValidationDto siteMapingValidation =
          liftApiService.validate(
              siteMappingUpdateModel, LiftEntityName.SITE_MAPPING, locale, source);
      if (!siteMapingValidation.getErrorDetails().isEmpty()) {
        siteMapingValidation.getErrorDetails().stream()
            .forEach(validation -> validation.setEntity(SITEMAPPING));
        throw new CustomDEExceptions(
            JsonUtils.toJsonWithoutPrettyPrinter(siteMapingValidation.getErrorDetails()));
      }
      log.debug("SITE MAPPINGS VALIDATED");

      site.getSiteDocument()
          .setExternalId(liftSitesServiceImpl.createSite(site.getSiteDocument(), locale, source));

      liftSiteMappingsService.createSiteMapping(site.getSiteDocument(), locale, source);
    } else {
      liftUtils.siteValidationError(user, salesforceAccount, site, locale, source);
    }
  }

  @Override
  public SiteDto update(SiteDto siteDto, Locale locale, ResourceBundleMessageSource source)
      throws NotFoundDEException, JsonProcessingException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    Accounts byAccountId = accountsRepository.findByAccountId(siteDto.getAccountId().toString());
    if (byAccountId == null) {
      throw new NotFoundDEException(
          String.format(ITEM_NOT_FOUND, "Account", siteDto.getAccountId()));
    }

    Sites siteById =
        sitesRepository.findBySiteId(siteDto.getId() != null ? siteDto.getId().toString() : null);
    if (siteById == null) {
      throw new NotFoundDEException(String.format(ITEM_NOT_FOUND, "Site", siteDto.getId()));
    }
    Sites siteToPersist = dtoToModel(siteDto, byAccountId);
    siteToPersist.setId(siteById.getId());
    // SalesForce Field
    siteToPersist.getSiteDocument().setExternalId(siteById.getSiteDocument().getExternalId());
    siteToPersist
        .getSiteDocument()
        .setCreateUser(
            siteById.getSiteDocument().getCreateUser() != null
                ? siteById.getSiteDocument().getCreateUser()
                : userServiceImpl.getCurrentLoggedInUser());

    if (DataSource.LIFT.equals(byAccountId.getAccountDocument().getDataSource())) {
      validateSalesforceData(siteToPersist, byAccountId, locale, source);
    }

    siteToPersist.setCreatedDate(siteById.getCreatedDate());
    // retain previous state of pens, visits and siteMappings
    if (siteToPersist.getSiteDocument() != null && siteById.getSiteDocument() != null) {
      siteToPersist.getSiteDocument().setVisits(siteById.getSiteDocument().getVisits());
      siteToPersist.getSiteDocument().setBarns(siteById.getSiteDocument().getBarns());
      //
      siteToPersist
          .getSiteDocument()
          .setDataSourceMappings(siteById.getSiteDocument().getDataSourceMappings());
      //      updateSiteMappings(siteToPersist);
    }
    // update status
    // updateVisitStatus(siteToPersist);

    // update foreign keys
    updateForeignKeys(siteToPersist);
    setSiteProperties(byAccountId.getAccountDocument().getId(), siteToPersist, false);
    if (Objects.isNull(siteToPersist.getSiteDocument().getDataSourceMappings())
        || siteToPersist.getSiteDocument().getDataSourceMappings().isEmpty()) {
      siteMappingServiceImpl.createLiftSiteMapping(siteToPersist);
    }
    siteToPersist = sitesRepository.saveAndFlush(siteToPersist);
    updateAccounts(siteToPersist.getSiteDocument().getVisits(), byAccountId);
    Map<String, Long> siteToPenCount =
        getPenCountBySiteId(new PageImpl<>(Arrays.asList(siteToPersist)));
    return modelToDto(siteToPersist, siteToPenCount);
  }

  public void validateSalesforceData(
      Sites site, Accounts salesforceAccount, Locale locale, ResourceBundleMessageSource source)
      throws IllegalAccessException, ClassNotFoundException, JsonProcessingException,
          CustomDEExceptions {
    log.debug("Checking for user: {}", userServiceImpl.getCurrentLoggedInUser());
    User user = liftUserService.findOwner(userServiceImpl.getCurrentLoggedInUser());
    if (salesforceAccount.getAccountDocument().getGoldenRecordId() != null
        && user != null
        && site.getSiteDocument().getExternalId() != null) {
      SiteUpdateModel siteUpdateModel =
          liftSitesServiceImpl.documentToModel(site.getSiteDocument());
      siteUpdateModel.setAccountId(salesforceAccount.getAccountDocument().getGoldenRecordId());
      log.debug("VALIDATING SITE");
      PayloadValidationDto siteValidation =
          liftApiService.validate(siteUpdateModel, LiftEntityName.SITE, locale, source);
      if (!siteValidation.getErrorDetails().isEmpty()) {
        siteValidation.getErrorDetails().stream().forEach(validation -> validation.setEntity(SITE));
        throw new CustomDEExceptions(
            JsonUtils.toJsonWithoutPrettyPrinter(siteValidation.getErrorDetails()));
      }
      log.debug("SITE VALIDATED");
    } else {
      log.error(
          "Site validation failed. GoldenRecordId: {}, user: {}, ExternalId: {}, SiteId: {}",
          salesforceAccount.getAccountDocument().getGoldenRecordId(),
          user,
          site.getSiteDocument().getExternalId(),
          site.getSiteDocument().getId().toString());
      liftUtils.siteValidationError(user, salesforceAccount, site, locale, source);
    }
  }

  public List<Sites> getAllUnsyncedSites(DataSource dataSource) {
    return sitesRepository.findAllBySiteDocumentNeedsSyncAndSiteDocumentDataSource(
        "true", dataSource.toString());
  }

  private SiteDto modelToDto(Sites site, Map<String, Long> siteToPenCount) {
    // filterPensOnSyncDate(site, lastSyncTime);
    SiteDto build =
        SiteDto.builder()
            .id(site.getSiteDocument().getId())
            .accountId(site.getSiteDocument().getAccountId())
            .externalAccountId(site.getSiteDocument().getExternalAccountId())
            .createTimeUtc(site.getCreatedDate().toInstant())
            .lastSyncTimeUtc(site.getSiteDocument().getLastSyncTimeUtc())
            .lastModifiedTimeUtc(site.getUpdatedDate().toInstant())
            .siteName(site.getSiteDocument().getSiteName())
            .currentMilkPrice(site.getSiteDocument().getCurrentMilkPrice())
            .daysInMilk(site.getSiteDocument().getDaysInMilk())
            .milkingSystemType(site.getSiteDocument().getMilkingSystemType())
            .dryMatterIntake(site.getSiteDocument().getDryMatterIntake())
            .lactatingAnimal(site.getSiteDocument().getLactatingAnimal())
            .milk(site.getSiteDocument().getMilk())
            .siteMappings(
                SiteMapper.modelToDtoForSiteMappings(
                    site.getSiteDocument().getDataSourceMappings()))
            .milkFatPercent(site.getSiteDocument().getMilkFatPercent())
            .milkProteinPercent(site.getSiteDocument().getMilkProteinPercent())
            .milkOtherSolidsPercent(site.getSiteDocument().getMilkOtherSolidsPercent())
            .milkSomaticCellCount(site.getSiteDocument().getMilkSomaticCellCount())
            .bacteriaCellCount(site.getSiteDocument().getBacteriaCellCount())
            .netEnergyOfLactationDairy(site.getSiteDocument().getNetEnergyOfLactationDairy())
            .rationCost(site.getSiteDocument().getRationCost())
            .barns(
                SiteMapper.modelToDtoForBarn(
                    site.getSiteDocument().getBarns())) // will not be part of site
            .asFedIntake(site.getSiteDocument().getAsFedIntake())
            .origination(site.getSiteDocument().getOrigination())
            .dDWLastUpdatedDate(site.getSiteDocument().getDDWLastUpdatedDate())
            .numberOfParlorStalls(site.getSiteDocument().getNumberOfParlorStalls())
            .localId(site.getLocalId())
            .deleted(site.getSiteDocument().isDeleted())
            .isNew(site.getSiteDocument().isNew())
            .penCount(calculatePenCount(site, siteToPenCount))
            .dateOfLastVisit(site.getSiteDocument().getDateOfLastVisit())
            .keys(site.getSiteDocument().getKeys())
            .hasReport(site.getSiteDocument().getHasReport())
            .build();
    return mapDefaultModelAttributes(site, build);
  }

  //  private Instant findDateOfLastVisit(List<SiteVisit> visits) {
  //    if (visits != null) {
  //      Optional<SiteVisit> first =
  //          visits.stream().max(Comparator.comparing(SiteVisit::getVisitDate));
  //      if (first.isPresent()) {
  //        return first.get().getVisitDate();
  //      }
  //    }
  //    return null;
  //  }

  /*  private void updateVisitStatus(Sites sites) {
    for (SiteVisit visitItem :
        sites.getSiteDocument().getVisits() != null
            ? sites.getSiteDocument().getVisits()
            : new ArrayList<SiteVisit>()) {
      if (visitItem.getStatus() != null && visitItem.getStatus() != VisitStatus.IsDeleted) {
        LocalDateTime visitDate =
            DateTimeUtils.instantToLocalDateTime(
                visitItem.getVisitDate() == null ? Instant.now() : visitItem.getVisitDate(),
                ZoneOffset.UTC);
        if (Duration.between(visitDate, LocalDateTime.now()).toDays() > autoPublishVisitDays) {
          visitItem.setStatus(VisitStatus.Published);
        } else {
          visitItem.setStatus(VisitStatus.InProgress);
        }
      }
    }
  }*/

  private void updateForeignKeys(Sites site) {
    // update pens with site and barnId
    if (site.getSiteDocument().getBarns() != null) {
      site.getSiteDocument()
          .getBarns()
          .forEach(
              barn -> {
                if (barn.getId() == null) {
                  barn.setId(UUID.randomUUID());
                }
              });
    }

    // TODO :  need to update visit uuids as well

    /*"Visits": [
    {
    	"Status": 1,
    		"VisitDate": "2020-11-06T15:52:59.889975Z",
    		"VisitName": "novembre 6, 2020",
    		"LabyrinthVisitId": "5dd83bb5-4a84-452c-af22-42777076968a"
    },
    {
    	"Status": 1,
    		"VisitDate": "2021-11-26T14:59:57.624572Z",
    		"VisitName": "novembre 26, 2021",
    		"LabyrinthVisitId": "d102fd24-8a3f-4f32-a16a-5d119ed67c7b"
    }
    	]*/

    // update site mappings
    if (site.getSiteDocument().getDataSourceMappings() != null) {
      site.getSiteDocument().getDataSourceMappings().stream()
          .filter(sm -> LM_SITE_SYSTEM_NAME.equals(sm.getSystemName()))
          .forEach(s -> s.setSystemId(site.getSiteDocument().getId().toString()));
    }
  }

  private Sites dtoToModel(SiteDto siteDto, Accounts account) {
    String goldenRecId = account.getAccountDocument().getGoldenRecordId();
    return Sites.builder()
        .deleted(siteDto.isDeleted())
        .localId(siteDto.getLocalId())
        .siteDocument(
            SiteDocument.builder()
                .id(siteDto.getId() != null ? siteDto.getId() : UUID.randomUUID())
                .accountId(account.getAccountDocument().getId())
                .externalAccountId(goldenRecId)
                // .createTimeUtc(Instant.now())
                .lastSyncTimeUtc(Instant.now())
                .lastModifiedTimeUtc(Instant.now())
                .siteName(siteDto.getSiteName())
                .currentMilkPrice(siteDto.getCurrentMilkPrice())
                .daysInMilk(siteDto.getDaysInMilk())
                .milkingSystemType(siteDto.getMilkingSystemType())
                .dryMatterIntake(siteDto.getDryMatterIntake())
                .lactatingAnimal(siteDto.getLactatingAnimal())
                .milk(siteDto.getMilk())
                .milkFatPercent(siteDto.getMilkFatPercent())
                .milkProteinPercent(siteDto.getMilkProteinPercent())
                .milkOtherSolidsPercent(siteDto.getMilkOtherSolidsPercent())
                .milkSomaticCellCount(siteDto.getMilkSomaticCellCount())
                .bacteriaCellCount(siteDto.getBacteriaCellCount())
                .netEnergyOfLactationDairy(siteDto.getNetEnergyOfLactationDairy())
                .rationCost(siteDto.getRationCost())
                // .barns(siteDto.getBarns())
                // .visits(siteDto.getVisits())
                // public List<Diet> Diets =new List<Diet>();
                .asFedIntake(siteDto.getAsFedIntake())
                // .siteMappings(SiteMapper.dtoToModelForSiteMappings(siteDto.getSiteMappings()))
                .origination(siteDto.getOrigination())
                .dateOfLastVisit(siteDto.getDateOfLastVisit())
                .dDWLastUpdatedDate(siteDto.getDDWLastUpdatedDate())
                .numberOfParlorStalls(siteDto.getNumberOfParlorStalls())
                .needsSync(siteDto.getId() != null)
                .dataSource(account.getAccountDocument().getDataSource())
                .isDeleted(siteDto.isDeleted())
                .isNew(siteDto.isNew())
                .keys(siteDto.getKeys())
                .hasReport(siteDto.getHasReport())
                .build())
        .build();
  }

  private void updateAccounts(List<SiteVisit> visits, Accounts account) {
    account
        .getAccountDocument()
        .setSiteCount(
            sitesRepository.countByAccountId(account.getAccountDocument().getId().toString()));
    // time stamps update
    account.getAccountDocument().setLastUpdateUserId(userServiceImpl.getCurrentLoggedInUser());
    account
        .getAccountDocument()
        .setLastModificationDate(DateEpoch.builder().date(Instant.now()).build());
    account.getAccountDocument().setLastModifiedBy(userServiceImpl.getCurrentLoggedInUser());
    account.getAccountDocument().setLastModifiedTimeUtc(Instant.now());
    account.getAccountDocument().setLastSyncTimeUtc(Instant.now());

    accountsRepository.saveAndFlush(account);
  }

  private void setSiteProperties(UUID accountId, Sites site, boolean isCreate) {
    if (site.getSiteDocument() == null) {
      site.setSiteDocument(new SiteDocument());
    }
    site.getSiteDocument().setLastSyncTimeUtc(Instant.now());
    site.getSiteDocument().setAccountId(accountId);
    if (isCreate) {
      site.getSiteDocument().setCreateTimeUtc(Instant.now());
      site.getSiteDocument().setCreateUser(userServiceImpl.getCurrentLoggedInUser());
    } else {
      site.getSiteDocument().setLastModifiedTimeUtc(Instant.now());
      site.getSiteDocument().setLastModifyUser(userServiceImpl.getCurrentLoggedInUser());
    }
  }

  private Integer calculatePenCount(Sites site, Map<String, Long> siteToPenCount) {
    return siteToPenCount
        .getOrDefault(
            site.getSiteDocument().getId() != null
                ? site.getSiteDocument().getId().toString()
                : null,
            Long.valueOf(0))
        .intValue();
  }

  private SiteDto mapDefaultModelAttributes(Sites site, SiteDto siteDto) {
    siteDto.setCreatedDate(site.getCreatedDate().toInstant());
    siteDto.setUpdatedDate(site.getUpdatedDate().toInstant());
    siteDto.setLocalId(site.getLocalId());
    siteDto.setDeleted(site.getSiteDocument().isDeleted());
    siteDto.setId(site.getSiteDocument().getId());

    // empty out the pens list from barn
    // siteDto.getBarns().stream().findFirst().orElse(new Barn()).setPens(null);
    return siteDto;
  }
}
