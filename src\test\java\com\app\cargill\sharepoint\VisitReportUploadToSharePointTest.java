/* Cargill Inc.(C) 2022 */
package com.app.cargill.sharepoint;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.app.cargill.confproperties.SharepointProperties;
import com.app.cargill.constants.VisitReportType;
import com.app.cargill.crescendo.service.ICrescendoActivityService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.document.EventDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.dto.VisitReportDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Activities;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.impl.DDWReportServiceImpl;
import com.app.cargill.sf.cc.service.LiftEventService;
import com.app.cargill.sharepoint.model.VisitReport;
import com.app.cargill.sharepoint.service.SharePointWebClientFactory;
import com.app.cargill.sharepoint.service.VisitReportUploadToSharePoint;
import com.app.cargill.utils.JsonUtils;
import com.app.cargill.utils.MinimalPdf;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.io.IOException;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.WebClient;

@RunWith(MockitoJUnitRunner.class)
public class VisitReportUploadToSharePointTest {

  @InjectMocks private VisitReportUploadToSharePoint underTest;

  @Mock private SharepointProperties sharepointProperties;

  @Mock private SharepointServiceCalls sharepointServiceCalls;
  @Mock private WebClient.Builder webClientBuilder;
  @Mock private SharePointWebClientFactory webClientFactory;
  @Captor private ArgumentCaptor<String> urlCapture;
  @Mock DDWReportServiceImpl dDWReportServiceImpl;
  @Mock private ICrescendoActivityService crescendoActivityService;
  @Captor private ArgumentCaptor<HttpEntity<String>> entityCapture;

  @Captor private ArgumentCaptor<Class<?>> responseTypeCapture;

  @Mock private Oauth2Dto ccAuthBean;
  private static MockWebServer mockBackEnd;
  @Mock private VisitsRepository visitsRepository;
  @Mock private AccountsRepository accountsRepository;

  @Mock private ActivitiesRepository activitiesRepository;
  @Mock private LiftEventService liftEventService;
  @Mock private SitesRepository sitesRepository;

  @Before
  public void setUp() throws Exception {
    mockBackEnd = new MockWebServer();
    mockBackEnd.start();

    when(sharepointProperties.getInstanceUrl())
        .thenReturn("http://localhost:" + mockBackEnd.getPort() + "/sites/dairyenteligen");
    when(sharepointProperties.getCheckFolderExistsUrl())
        .thenReturn(
            "/sites/dairyenteligen/_api/web/GetFolderByServerRelativeUrl({0}/sites/dairyenteligen/Shared%20Documents/{1}{2})/Exists");
    when(sharepointProperties.getCreateFolderUrl())
        .thenReturn(
            "/sites/dairyenteligen/_api/Web/Folders/add({0}/sites/dairyenteligen/Shared%20Documents/{1}{2})");
    when(sharepointProperties.getVisitReportFileUrl())
        .thenReturn(
            "https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%20Documents/{0}/{1}");
    webClientFactory = new SharePointWebClientFactory(webClientBuilder);
    when(webClientFactory.createWebClient()).thenReturn(WebClient.builder().build());
    sharepointServiceCalls = new SharepointServiceCalls(webClientFactory, sharepointProperties);

    ccAuthBean.setAccessToken(UUID.randomUUID().toString());
    ccAuthBean.setResource("http://localhost:" + mockBackEnd.getPort() + "/");
    underTest =
        new VisitReportUploadToSharePoint(
            sharepointServiceCalls,
            sharepointProperties,
            dDWReportServiceImpl,
            visitsRepository,
            accountsRepository,
            activitiesRepository,
            liftEventService,
            sitesRepository,
            crescendoActivityService);
    when(dDWReportServiceImpl.fetchAccessToken()).thenReturn(ccAuthBean);
    when(dDWReportServiceImpl.fetchAccessToken())
        .thenReturn(
            Oauth2Dto.builder()
                .accessToken(UUID.randomUUID().toString())
                .resource("http://localhost:" + mockBackEnd.getPort() + "/")
                .build());
  }

  @After
  public void tearDown() throws IOException {
    mockBackEnd.shutdown();
  }

  @Test
  public void whenUploadVisitReportToSharePointReturnsSuccess() throws JsonProcessingException {
    String tokenResponse = JsonUtils.toJson(Oauth2Dto.builder().accessToken("testToken").build());

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    List<VisitReport> build =
        List.of(
            VisitReport.builder()
                .visitId("visitId")
                .goldenRecordId("goldenrecordid")
                .reportFile(MinimalPdf.data)
                .accountId("accountId")
                .reportType(VisitReportType.SiteVisit)
                .reportDate(Instant.now().toString())
                .build());
    underTest.uploadVisitReportToSharePoint(build);
    assertNotNull(build.stream().findFirst().get().getUrl());
  }

  @Test
  public void whenUploadOfflineVisitReportHasNoVisitIdExceptionIsThrown()
      throws JsonProcessingException, CustomDEExceptions {
    String tokenResponse = JsonUtils.toJson(Oauth2Dto.builder().accessToken("token").build());

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));

    List<VisitReport> build =
        List.of(
            VisitReport.builder()
                .visitId("visitId")
                .goldenRecordId("goldenrecordid")
                .reportFile(MinimalPdf.data)
                .accountId("accountId")
                .reportType(VisitReportType.SiteVisit)
                .reportDate(Instant.now().toString())
                .build());
    underTest.uploadOfflineVisitReportToSharePoint(UUID.randomUUID().toString(), new byte[10]);
    assertNotNull(build.stream().findFirst().get().getAccountId());
  }

  @Test
  public void whenUploadOfflineVisitReportToSharePointReturnsSuccess()
      throws JsonProcessingException, CustomDEExceptions {
    String tokenResponse = JsonUtils.toJson(Oauth2Dto.builder().accessToken("token").build());

    when(visitsRepository.findByVisitId(any())).thenReturn(getVisitData());

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));

    List<VisitReport> build =
        List.of(
            VisitReport.builder()
                .visitId("visitId")
                .goldenRecordId("goldenrecordid")
                .reportFile(MinimalPdf.data)
                .accountId("accountId")
                .reportType(VisitReportType.SiteVisit)
                .reportDate(Instant.now().toString())
                .build());
    underTest.uploadOfflineVisitReportToSharePoint(UUID.randomUUID().toString(), new byte[10]);
    assertNotNull(build.stream().findFirst().get().getAccountId());
  }

  @Test
  public void testUploadOfflineVisitReport_NullVisitId_ExceptionThrown() {
    // Arrange
    String visitId = null;
    byte[] bytes = new byte[10];

    // Act and Assert
    assertThrows(
        CustomDEExceptions.class,
        () -> underTest.uploadOfflineVisitReportToSharePoint(visitId, bytes));
  }

  @Test
  public void whenUploadVisitReportWidthDifferentReportTypeToSharePointReturnsSuccess()
      throws JsonProcessingException {
    String tokenResponse = JsonUtils.toJson(Oauth2Dto.builder().accessToken("testToken").build());

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    List<VisitReport> build =
        List.of(
            VisitReport.builder()
                .visitId("visitId")
                .goldenRecordId("goldenrecordid")
                .reportFile(MinimalPdf.data)
                .accountId("accountId")
                .reportType(VisitReportType.Tool)
                .reportDate(Instant.now().toString())
                .build());
    underTest.uploadVisitReportToSharePoint(build);
    assertNotNull(build.stream().findFirst().get().getUrl());
  }

  Visits getVisitData() {
    return Visits.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(VisitDocument.builder().id(UUID.randomUUID()).toString())
        .visitDocument(
            VisitDocument.builder()
                .id(UUID.randomUUID())
                .customerId(UUID.randomUUID())
                .visitDate(Instant.now())
                .build())
        .build();
  }

  Accounts getAccountData() {
    return Accounts.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(VisitDocument.builder().id(UUID.randomUUID()).toString())
        .accountDocument(
            AccountDocument.builder()
                .id(UUID.randomUUID())
                .goldenRecordId(UUID.randomUUID().toString())
                .build())
        .build();
  }

  @Test
  public void whenUploadVisitReportToSharePointWithByteArrayReturnsSuccess() {
    String tokenResponse = "{}";

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":false}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    when(visitsRepository.findByVisitId(anyString())).thenReturn(getVisitData());
    when(visitsRepository.save(any())).thenReturn(getVisitData());
    when(accountsRepository.findByAccountId(anyString())).thenReturn(getAccountData());
    when(activitiesRepository.findByVisitId(any()))
        .thenReturn(
            Activities.builder().activityDocument(ActivityDocument.builder().build()).build());
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .id(UUID.randomUUID())
                        .externalId(UUID.randomUUID().toString())
                        .build())
                .build());
    CompletableFuture<VisitReportDto> visitReportDtoCompletableFuture =
        underTest.uploadVisitReportToSharePoint(
            VisitReportDto.builder()
                .visitId(UUID.randomUUID())
                .visitDate(Instant.now().toString())
                .build(),
            MinimalPdf.data);
    assertNotNull(visitReportDtoCompletableFuture);
  }

  @Test
  public void whenCreateEventIsCalledCorrectDocumentIsReturned() {
    ActivityDocument activityDocument =
        ActivityDocument.builder()
            .visitId(UUID.randomUUID())
            .activityDateTime(Instant.now())
            .id(UUID.randomUUID())
            .endDateTime(Instant.now())
            .eventSfdcId(UUID.randomUUID().toString())
            .subject("Test")
            .reportLink("test.com")
            .ownerId("***********")
            .accountId(UUID.randomUUID())
            .sfdcAccountId("0014x00000jEXYkAAO")
            .build();
    Visits visit =
        Visits.builder()
            .visitDocument(
                VisitDocument.builder().id(UUID.randomUUID()).siteId(UUID.randomUUID()).build())
            .build();

    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .id(UUID.randomUUID())
                        .externalId(UUID.randomUUID().toString())
                        .build())
                .build());

    EventDocument result = underTest.createEvent(activityDocument, visit);

    assertNotNull(result);
  }

  public void whenUploadVisitReportToSharePointWithByteArrayAndFolderExistsReturnsSuccess() {
    String tokenResponse = "{}";

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":true}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody("{\"d\":{\"Exists\":true}}")
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json")
            .setResponseCode(HttpStatus.OK.value()));

    when(visitsRepository.findByVisitId(anyString())).thenReturn(getVisitData());
    when(visitsRepository.save(any())).thenReturn(getVisitData());
    when(accountsRepository.findByAccountId(anyString())).thenReturn(getAccountData());
    CompletableFuture<VisitReportDto> visitReportDtoCompletableFuture =
        underTest.uploadVisitReportToSharePoint(
            VisitReportDto.builder().visitId(UUID.randomUUID()).build(), MinimalPdf.data);
    assertNotNull(visitReportDtoCompletableFuture);
  }

  @Test
  public void whenCustomerIdIsNullReturnsSuccess() {
    Visits visitData = getVisitData();
    visitData.getVisitDocument().setCustomerId(null);
    when(visitsRepository.findByVisitId(anyString())).thenReturn(visitData);

    CompletableFuture<VisitReportDto> visitReportDtoCompletableFuture =
        underTest.uploadVisitReportToSharePoint(
            VisitReportDto.builder().visitId(UUID.randomUUID()).build(), MinimalPdf.data);
    assertNotNull(visitReportDtoCompletableFuture);
  }

  @Test
  public void whenAccountDocumentIsNullReturnsSuccess() {
    Visits visitData = getVisitData();
    Accounts accountData = getAccountData();
    accountData.setAccountDocument(null);
    when(visitsRepository.findByVisitId(anyString())).thenReturn(visitData);

    when(accountsRepository.findByAccountId(anyString())).thenReturn(accountData);
    CompletableFuture<VisitReportDto> visitReportDtoCompletableFuture =
        underTest.uploadVisitReportToSharePoint(
            VisitReportDto.builder().visitId(UUID.randomUUID()).build(), MinimalPdf.data);
    assertNotNull(visitReportDtoCompletableFuture);
  }

  @Test
  public void whenUrlIsCorrect() {
    WebClient webClient =
        webClientFactory.createWebClient(
            "http://localhost:" + mockBackEnd.getPort() + "/sites/dairyenteligen");
    assertNotNull(webClient);
  }
}
