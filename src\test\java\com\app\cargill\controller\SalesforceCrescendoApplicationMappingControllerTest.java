/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.crescendo.service.ICrescendoApplicationMappingService;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import java.util.Locale;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoApplicationMappingControllerTest {

  @Mock private ICrescendoApplicationMappingService crescendoApplicationMappingService;
  @Mock private ResourceBundleMessageSource bundleMessageSource;

  @InjectMocks private SalesforceCrescendoApplicationMappingController controller;

  private SiteMappingDocument testSiteMapping;

  @BeforeEach
  void setUp() {
    testSiteMapping = createTestSiteMappingDocument();
  }

  @Test
  void createApplicationMapping_Success() throws CustomDEExceptions {
    // Arrange
    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoApplicationMappingService.createApplicationMapping(
            eq(testSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    String result = controller.createApplicationMapping(testSiteMapping);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            eq(testSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_ThrowsCustomDEExceptions_WhenServiceThrowsException()
      throws CustomDEExceptions {
    // Arrange
    when(crescendoApplicationMappingService.createApplicationMapping(
            any(SiteMappingDocument.class), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenThrow(new CustomDEExceptions("Service error"));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class, () -> controller.createApplicationMapping(testSiteMapping));

    assertNotNull(exception);
    assertEquals("Service error", exception.getMessage());
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            eq(testSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_WithNullSiteMapping_CallsService() throws CustomDEExceptions {
    // Arrange
    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoApplicationMappingService.createApplicationMapping(
            eq(null), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    String result = controller.createApplicationMapping(null);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(eq(null), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_WithMinimalSiteMapping_Success() throws CustomDEExceptions {
    // Arrange
    SiteMappingDocument minimalSiteMapping =
        SiteMappingDocument.builder()
            .labyrinthSiteId(UUID.fromString("1b04305c-7f6a-493e-a581-962cd8e36dee"))
            .build();

    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoApplicationMappingService.createApplicationMapping(
            eq(minimalSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    String result = controller.createApplicationMapping(minimalSiteMapping);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            eq(minimalSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_WithComplexSiteMapping_Success() throws CustomDEExceptions {
    // Arrange
    SiteMappingDocument complexSiteMapping =
        SiteMappingDocument.builder()
            .id(UUID.randomUUID())
            .labyrinthSiteId(UUID.fromString("1b04305c-7f6a-493e-a581-962cd8e36dee"))
            .ddwHerdId("10665")
            .maxSiteId(UUID.fromString("b7a69930-387e-40c4-a46b-4a69f6933663"))
            .labyrinthAccountId(UUID.fromString("ea9b39f0-f355-4f2d-b788-57fa96a01cfb"))
            .milkProcessorId("MP123")
            .dcgoId("DCGO456")
            .build();

    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoApplicationMappingService.createApplicationMapping(
            eq(complexSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    String result = controller.createApplicationMapping(complexSiteMapping);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            eq(complexSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_VerifyLocaleIsAlwaysEnglish() throws CustomDEExceptions {
    // Arrange
    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoApplicationMappingService.createApplicationMapping(
            any(SiteMappingDocument.class), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    controller.createApplicationMapping(testSiteMapping);

    // Assert
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            any(SiteMappingDocument.class), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_VerifyBundleMessageSourceIsPassedCorrectly()
      throws CustomDEExceptions {
    // Arrange
    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoApplicationMappingService.createApplicationMapping(
            any(SiteMappingDocument.class), any(), any(Locale.class), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    controller.createApplicationMapping(testSiteMapping);

    // Assert
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            any(SiteMappingDocument.class), any(), any(Locale.class), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_ServiceReturnsEmptyString_ReturnsEmptyString()
      throws CustomDEExceptions {
    // Arrange
    when(crescendoApplicationMappingService.createApplicationMapping(
            any(SiteMappingDocument.class), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn("");

    // Act
    String result = controller.createApplicationMapping(testSiteMapping);

    // Assert
    assertNotNull(result);
    assertEquals("", result);
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            eq(testSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createApplicationMapping_ServiceReturnsNull_ReturnsNull() throws CustomDEExceptions {
    // Arrange
    when(crescendoApplicationMappingService.createApplicationMapping(
            any(SiteMappingDocument.class), any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(null);

    // Act
    String result = controller.createApplicationMapping(testSiteMapping);

    // Assert
    assertNull(result);
    verify(crescendoApplicationMappingService)
        .createApplicationMapping(
            eq(testSiteMapping), any(), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  private SiteMappingDocument createTestSiteMappingDocument() {
    return SiteMappingDocument.builder()
        .id(UUID.fromString("8d591af8-0ab2-41b6-9a8c-7ed4f70a389d"))
        .labyrinthSiteId(UUID.fromString("1b04305c-7f6a-493e-a581-962cd8e36dee"))
        .ddwHerdId("10665")
        .maxSiteId(UUID.fromString("b7a69930-387e-40c4-a46b-4a69f6933663"))
        .labyrinthAccountId(UUID.fromString("ea9b39f0-f355-4f2d-b788-57fa96a01cfb"))
        .milkProcessorId(null)
        .dcgoId(null)
        .build();
  }
}
